/* Complete CSS styling for Settings Dashboard */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Layout utilities */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.max-w-7xl { max-width: 80rem; margin: 0 auto; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.p-6 { padding: 1.5rem; }
.p-4 { padding: 1rem; }
.p-3 { padding: 0.75rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1 1 0%; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

/* Spacing utilities */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mb-2 { margin-top: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-3 { margin-left: 0.75rem; }

/* Sizing utilities */
.h-5 { height: 1.25rem; }
.w-5 { width: 1.25rem; }
.h-6 { height: 1.5rem; }
.w-6 { width: 1.5rem; }
.h-20 { height: 5rem; }
.w-20 { width: 5rem; }
.h-32 { height: 8rem; }
.w-32 { width: 8rem; }
.h-64 { height: 16rem; }
.w-11 { width: 2.75rem; }
.w-full { width: 100%; }
.block { display: block; }

/* Border utilities */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-r-2 { border-right-width: 2px; }
.border-dashed { border-style: dashed; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-blue-200 { border-color: #bfdbfe; }
.border-blue-500 { border-color: #3b82f6; }
.border-blue-600 { border-color: #2563eb; }
.border-blue-700 { border-color: #1d4ed8; }
.border-yellow-200 { border-color: #fef3c7; }
.border-transparent { border-color: transparent; }

/* Border radius utilities */
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Background utilities */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-yellow-50 { background-color: #fffbeb; }

/* Text utilities */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }
.text-blue-800 { color: #1e40af; }
.text-white { color: #ffffff; }
.text-yellow-700 { color: #a16207; }
.text-yellow-800 { color: #92400e; }
.text-red-600 { color: #dc2626; }

/* Shadow utilities */
.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }

/* Animation utilities */
.animate-spin { animation: spin 1s linear infinite; }
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Interactive utilities */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* Focus utilities */
.focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\\:ring-offset-2:focus { box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(59, 130, 246, 0.5); }
.focus\\:ring-blue-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\\:border-blue-500:focus { border-color: #3b82f6; }

/* Hover utilities */
.hover\\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\\:text-gray-900:hover { color: #111827; }
.hover\\:text-blue-800:hover { color: #1e40af; }
.hover\\:text-red-800:hover { color: #991b1b; }

/* Disabled utilities */
.disabled\\:bg-gray-50:disabled { background-color: #f9fafb; }
.disabled\\:text-gray-500:disabled { color: #6b7280; }
.opacity-50 { opacity: 0.5; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

/* Form controls */
input, select, textarea {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input:disabled, select:disabled, textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

/* Button styles */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Switch component styles */
.switch-container {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 9999px;
  border: 2px solid transparent;
  transition: background-color 0.2s ease-in-out;
}

.switch-container:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.switch-thumb {
  pointer-events: none;
  display: inline-block;
  height: 1.25rem;
  width: 1.25rem;
  transform: translateX(0);
  border-radius: 9999px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  transition: transform 0.2s ease-in-out;
}

.switch-thumb.translate-x-5 {
  transform: translateX(1.25rem);
}

/* Tab styles */
.tab-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tab-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  color: #4b5563;
}

.tab-button:hover {
  color: #111827;
  background-color: #f9fafb;
}

.tab-button.active {
  background-color: #dbeafe;
  color: #1d4ed8;
  border-right: 2px solid #1d4ed8;
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\\:px-0 { padding-left: 0; padding-right: 0; }
  .sm\\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .sm\\:col-span-2 { grid-column: span 2 / span 2; }
  .sm\\:w-32 { width: 8rem; }
}

@media (min-width: 1024px) {
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\\:mb-0 { margin-bottom: 0; }
  .lg\\:flex-row { flex-direction: row; }
  .lg\\:w-64 { width: 16rem; }
  .lg\\:space-x-8 > * + * { margin-left: 2rem; }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Utility classes for specific components */
.shrink-0 { flex-shrink: 0; }
.object-cover { object-fit: cover; }
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Alert styles */
.alert {
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid;
}

.alert-yellow {
  background-color: #fffbeb;
  border-color: #fef3c7;
  color: #92400e;
}

.alert-blue {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
}
