/* Basic CSS Reset and Tailwind-like utilities */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Basic utility classes to replace Tailwind */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.max-w-7xl { max-width: 80rem; margin: 0 auto; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
.border-4 { border-width: 4px; }
.border-dashed { border-style: dashed; }
.border-gray-200 { border-color: #e5e7eb; }
.rounded-lg { border-radius: 0.5rem; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.h-64 { height: 16rem; }
.animate-spin { animation: spin 1s linear infinite; }
.rounded-full { border-radius: 9999px; }
.h-32 { height: 8rem; }
.w-32 { width: 8rem; }
.border-b-2 { border-bottom-width: 2px; }
.border-blue-600 { border-color: #2563eb; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.font-bold { font-weight: 700; }
.text-gray-900 { color: #111827; }
.mt-2 { margin-top: 0.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-gray-600 { color: #4b5563; }
.mb-8 { margin-bottom: 2rem; }
.lg\\:mb-0 { margin-bottom: 0; }
.flex-col { flex-direction: column; }
.lg\\:flex-row { flex-direction: row; }
.lg\\:space-x-8 > * + * { margin-left: 2rem; }
.lg\\:w-64 { width: 16rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.font-medium { font-weight: 500; }
.rounded-md { border-radius: 0.375rem; }
.bg-blue-100 { background-color: #dbeafe; }
.text-blue-700 { color: #1d4ed8; }
.border-r-2 { border-right-width: 2px; }
.border-blue-700 { border-color: #1d4ed8; }
.hover\\:text-gray-900:hover { color: #111827; }
.hover\\:bg-gray-50:hover { background-color: #f9fafb; }
.flex-1 { flex: 1 1 0%; }
.focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.bg-white { background-color: #ffffff; }
.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.border-b { border-bottom-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-gray-900 { color: #111827; }
.mr-2 { margin-right: 0.5rem; }
.h-5 { height: 1.25rem; }
.w-5 { width: 1.25rem; }
.p-6 { padding: 1.5rem; }
