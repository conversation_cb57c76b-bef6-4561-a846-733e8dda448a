{"ast": null, "code": "import { useRef as o } from \"react\";\nimport { useWindowEvent as t } from './use-window-event.js';\nvar a = (r => (r[r.Forwards = 0] = \"Forwards\", r[r.Backwards = 1] = \"Backwards\", r))(a || {});\nfunction u() {\n  let e = o(0);\n  return t(!0, \"keydown\", r => {\n    r.key === \"Tab\" && (e.current = r.shiftKey ? 1 : 0);\n  }, !0), e;\n}\nexport { a as Direction, u as useTabDirection };", "map": {"version": 3, "names": ["useRef", "o", "useWindowEvent", "t", "a", "r", "Forwards", "Backwards", "u", "e", "key", "current", "shift<PERSON>ey", "Direction", "useTabDirection"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-tab-direction.js"], "sourcesContent": ["import{useRef as o}from\"react\";import{useWindowEvent as t}from'./use-window-event.js';var a=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(a||{});function u(){let e=o(0);return t(!0,\"keydown\",r=>{r.key===\"Tab\"&&(e.current=r.shiftKey?1:0)},!0),e}export{a as Direction,u as useTabDirection};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACD,CAAC,CAACA,CAAC,CAACE,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACR,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOE,CAAC,CAAC,CAAC,CAAC,EAAC,SAAS,EAACE,CAAC,IAAE;IAACA,CAAC,CAACK,GAAG,KAAG,KAAK,KAAGD,CAAC,CAACE,OAAO,GAACN,CAAC,CAACO,QAAQ,GAAC,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACH,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIS,SAAS,EAACL,CAAC,IAAIM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}