{"ast": null, "code": "import { getFocusableTreeWalker as $9bf71ea28793e738$export$2d6ec8fc375ceafa } from \"./FocusScope.mjs\";\nimport { useLayoutEffect as $hGAaG$useLayoutEffect } from \"@react-aria/utils\";\nimport { useState as $hGAaG$useState } from \"react\";\n\n/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $83013635b024ae3d$export$eac1895992b9f3d6(ref, options) {\n  let isDisabled = options === null || options === void 0 ? void 0 : options.isDisabled;\n  let [hasTabbableChild, setHasTabbableChild] = (0, $hGAaG$useState)(false);\n  (0, $hGAaG$useLayoutEffect)(() => {\n    if ((ref === null || ref === void 0 ? void 0 : ref.current) && !isDisabled) {\n      let update = () => {\n        if (ref.current) {\n          let walker = (0, $9bf71ea28793e738$export$2d6ec8fc375ceafa)(ref.current, {\n            tabbable: true\n          });\n          setHasTabbableChild(!!walker.nextNode());\n        }\n      };\n      update();\n      // Update when new elements are inserted, or the tabIndex/disabled attribute updates.\n      let observer = new MutationObserver(update);\n      observer.observe(ref.current, {\n        subtree: true,\n        childList: true,\n        attributes: true,\n        attributeFilter: ['tabIndex', 'disabled']\n      });\n      return () => {\n        // Disconnect mutation observer when a React update occurs on the top-level component\n        // so we update synchronously after re-rendering. Otherwise React will emit act warnings\n        // in tests since mutation observers fire asynchronously. The mutation observer is necessary\n        // so we also update if a child component re-renders and adds/removes something tabbable.\n        observer.disconnect();\n      };\n    }\n  });\n  return isDisabled ? false : hasTabbableChild;\n}\nexport { $83013635b024ae3d$export$eac1895992b9f3d6 as useHasTabbableChild };", "map": {"version": 3, "names": ["$83013635b024ae3d$export$eac1895992b9f3d6", "ref", "options", "isDisabled", "hasTabbableChild", "setHasTabbableChild", "$hGAaG$useState", "$hGAaG$useLayoutEffect", "current", "update", "walker", "$9bf71ea28793e738$export$2d6ec8fc375ceafa", "tabbable", "nextNode", "observer", "MutationObserver", "observe", "subtree", "childList", "attributes", "attributeFilter", "disconnect"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\useHasTabbableChild.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getFocusableTreeWalker} from './FocusScope';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useState} from 'react';\n\ninterface AriaHasTabbableChildOptions {\n  isDisabled?: boolean\n}\n\n// This was created for a special empty case of a component that can have child or\n// be empty, like Collection/Virtualizer/Table/ListView/etc. When these components\n// are empty they can have a message with a tabbable element, which is like them\n// being not empty, when it comes to focus and tab order.\n\n/**\n * Returns whether an element has a tabbable child, and updates as children change.\n * @private\n */\nexport function useHasTabbableChild(ref: RefObject<Element | null>, options?: AriaHasTabbableChildOptions): boolean {\n  let isDisabled = options?.isDisabled;\n  let [hasTabbableChild, setHasTabbableChild] = useState(false);\n\n  useLayoutEffect(() => {\n    if (ref?.current && !isDisabled) {\n      let update = () => {\n        if (ref.current) {\n          let walker = getFocusableTreeWalker(ref.current, {tabbable: true});\n          setHasTabbableChild(!!walker.nextNode());\n        }\n      };\n\n      update();\n\n      // Update when new elements are inserted, or the tabIndex/disabled attribute updates.\n      let observer = new MutationObserver(update);\n      observer.observe(ref.current, {\n        subtree: true,\n        childList: true,\n        attributes: true,\n        attributeFilter: ['tabIndex', 'disabled']\n      });\n\n      return () => {\n        // Disconnect mutation observer when a React update occurs on the top-level component\n        // so we update synchronously after re-rendering. Otherwise React will emit act warnings\n        // in tests since mutation observers fire asynchronously. The mutation observer is necessary\n        // so we also update if a child component re-renders and adds/removes something tabbable.\n        observer.disconnect();\n      };\n    }\n  });\n\n  return isDisabled ? false : hasTabbableChild;\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AA8BO,SAASA,0CAAoBC,GAA8B,EAAEC,OAAqC;EACvG,IAAIC,UAAA,GAAaD,OAAA,aAAAA,OAAA,uBAAAA,OAAA,CAASC,UAAU;EACpC,IAAI,CAACC,gBAAA,EAAkBC,mBAAA,CAAoB,GAAG,IAAAC,eAAO,EAAE;EAEvD,IAAAC,sBAAc,EAAE;IACd,IAAI,CAAAN,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAKO,OAAO,KAAI,CAACL,UAAA,EAAY;MAC/B,IAAIM,MAAA,GAASA,CAAA;QACX,IAAIR,GAAA,CAAIO,OAAO,EAAE;UACf,IAAIE,MAAA,GAAS,IAAAC,yCAAqB,EAAEV,GAAA,CAAIO,OAAO,EAAE;YAACI,QAAA,EAAU;UAAI;UAChEP,mBAAA,CAAoB,CAAC,CAACK,MAAA,CAAOG,QAAQ;QACvC;MACF;MAEAJ,MAAA;MAEA;MACA,IAAIK,QAAA,GAAW,IAAIC,gBAAA,CAAiBN,MAAA;MACpCK,QAAA,CAASE,OAAO,CAACf,GAAA,CAAIO,OAAO,EAAE;QAC5BS,OAAA,EAAS;QACTC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZC,eAAA,EAAiB,CAAC,YAAY;MAChC;MAEA,OAAO;QACL;QACA;QACA;QACA;QACAN,QAAA,CAASO,UAAU;MACrB;IACF;EACF;EAEA,OAAOlB,UAAA,GAAa,QAAQC,gBAAA;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}