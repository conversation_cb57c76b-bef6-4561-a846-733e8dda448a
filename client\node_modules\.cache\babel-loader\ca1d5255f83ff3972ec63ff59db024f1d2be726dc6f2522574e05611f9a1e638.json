{"ast": null, "code": "import r, { createContext as l, useContext as d } from \"react\";\nlet n = l(null);\nn.displayName = \"OpenClosedContext\";\nvar i = (e => (e[e.Open = 1] = \"Open\", e[e.Closed = 2] = \"Closed\", e[e.Closing = 4] = \"Closing\", e[e.Opening = 8] = \"Opening\", e))(i || {});\nfunction u() {\n  return d(n);\n}\nfunction c({\n  value: o,\n  children: t\n}) {\n  return r.createElement(n.Provider, {\n    value: o\n  }, t);\n}\nfunction s({\n  children: o\n}) {\n  return r.createElement(n.Provider, {\n    value: null\n  }, o);\n}\nexport { c as OpenClosedProvider, s as ResetOpenClosedProvider, i as State, u as useOpenClosed };", "map": {"version": 3, "names": ["r", "createContext", "l", "useContext", "d", "n", "displayName", "i", "e", "Open", "Closed", "Closing", "Opening", "u", "c", "value", "o", "children", "t", "createElement", "Provider", "s", "OpenClosedProvider", "ResetOpenClosedProvider", "State", "useOpenClosed"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/internal/open-closed.js"], "sourcesContent": ["import r,{createContext as l,useContext as d}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,IAAI,CAAC;AAACG,CAAC,CAACC,WAAW,GAAC,mBAAmB;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAACA,CAAC,CAACG,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACH,CAAC,CAACA,CAAC,CAACI,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACJ,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASM,CAACA,CAAA,EAAE;EAAC,OAAOT,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASS,CAACA,CAAC;EAACC,KAAK,EAACC,CAAC;EAACC,QAAQ,EAACC;AAAC,CAAC,EAAC;EAAC,OAAOlB,CAAC,CAACmB,aAAa,CAACd,CAAC,CAACe,QAAQ,EAAC;IAACL,KAAK,EAACC;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAAC;EAACJ,QAAQ,EAACD;AAAC,CAAC,EAAC;EAAC,OAAOhB,CAAC,CAACmB,aAAa,CAACd,CAAC,CAACe,QAAQ,EAAC;IAACL,KAAK,EAAC;EAAI,CAAC,EAACC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIQ,kBAAkB,EAACD,CAAC,IAAIE,uBAAuB,EAAChB,CAAC,IAAIiB,KAAK,EAACX,CAAC,IAAIY,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}