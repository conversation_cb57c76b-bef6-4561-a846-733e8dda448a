{"ast": null, "code": "\"use client\";\n\nimport { useFocusRing as A } from \"@react-aria/focus\";\nimport { useHover as c } from \"@react-aria/interactions\";\nimport { useMemo as E } from \"react\";\nimport { useId as P } from '../../hooks/use-id.js';\nimport { useDisabled as R } from '../../internal/disabled.js';\nimport { useProvidedId as g } from '../../internal/id.js';\nimport { forwardRefWithAs as v, mergeProps as _, useRender as D } from '../../utils/render.js';\nimport { useDescribedBy as F } from '../description/description.js';\nimport { useLabelledBy as h } from '../label/label.js';\nlet L = \"textarea\";\nfunction H(s, l) {\n  let i = P(),\n    d = g(),\n    n = R(),\n    {\n      id: p = d || `headlessui-textarea-${i}`,\n      disabled: e = n || !1,\n      autoFocus: r = !1,\n      invalid: a = !1,\n      ...T\n    } = s,\n    f = h(),\n    m = F(),\n    {\n      isFocused: o,\n      focusProps: u\n    } = A({\n      autoFocus: r\n    }),\n    {\n      isHovered: t,\n      hoverProps: b\n    } = c({\n      isDisabled: e\n    }),\n    y = _({\n      ref: l,\n      id: p,\n      \"aria-labelledby\": f,\n      \"aria-describedby\": m,\n      \"aria-invalid\": a ? \"true\" : void 0,\n      disabled: e || void 0,\n      autoFocus: r\n    }, u, b),\n    x = E(() => ({\n      disabled: e,\n      invalid: a,\n      hover: t,\n      focus: o,\n      autofocus: r\n    }), [e, a, t, o, r]);\n  return D()({\n    ourProps: y,\n    theirProps: T,\n    slot: x,\n    defaultTag: L,\n    name: \"Textarea\"\n  });\n}\nlet J = v(H);\nexport { J as Textarea };", "map": {"version": 3, "names": ["useFocusRing", "A", "useHover", "c", "useMemo", "E", "useId", "P", "useDisabled", "R", "useProvidedId", "g", "forwardRefWithAs", "v", "mergeProps", "_", "useRender", "D", "useDescribedBy", "F", "useLabelledBy", "h", "L", "H", "s", "l", "i", "d", "n", "id", "p", "disabled", "e", "autoFocus", "r", "invalid", "a", "T", "f", "m", "isFocused", "o", "focusProps", "u", "isHovered", "t", "hoverProps", "b", "isDisabled", "y", "ref", "x", "hover", "focus", "autofocus", "ourProps", "theirProps", "slot", "defaultTag", "name", "J", "Textarea"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/components/textarea/textarea.js"], "sourcesContent": ["\"use client\";import{useFocusRing as A}from\"@react-aria/focus\";import{useHover as c}from\"@react-aria/interactions\";import{useMemo as E}from\"react\";import{useId as P}from'../../hooks/use-id.js';import{useDisabled as R}from'../../internal/disabled.js';import{useProvidedId as g}from'../../internal/id.js';import{forwardRefWithAs as v,mergeProps as _,useRender as D}from'../../utils/render.js';import{useDescribedBy as F}from'../description/description.js';import{useLabelledBy as h}from'../label/label.js';let L=\"textarea\";function H(s,l){let i=P(),d=g(),n=R(),{id:p=d||`headlessui-textarea-${i}`,disabled:e=n||!1,autoFocus:r=!1,invalid:a=!1,...T}=s,f=h(),m=F(),{isFocused:o,focusProps:u}=A({autoFocus:r}),{isHovered:t,hoverProps:b}=c({isDisabled:e}),y=_({ref:l,id:p,\"aria-labelledby\":f,\"aria-describedby\":m,\"aria-invalid\":a?\"true\":void 0,disabled:e||void 0,autoFocus:r},u,b),x=E(()=>({disabled:e,invalid:a,hover:t,focus:o,autofocus:r}),[e,a,t,o,r]);return D()({ourProps:y,theirProps:T,slot:x,defaultTag:L,name:\"Textarea\"})}let J=v(H);export{J as Textarea};\n"], "mappings": "AAAA,YAAY;;AAAC,SAAOA,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAAC,UAAU;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,CAAC;IAACoB,CAAC,GAAChB,CAAC,CAAC,CAAC;IAACiB,CAAC,GAACnB,CAAC,CAAC,CAAC;IAAC;MAACoB,EAAE,EAACC,CAAC,GAACH,CAAC,IAAE,uBAAuBD,CAAC,EAAE;MAACK,QAAQ,EAACC,CAAC,GAACJ,CAAC,IAAE,CAAC,CAAC;MAACK,SAAS,EAACC,CAAC,GAAC,CAAC,CAAC;MAACC,OAAO,EAACC,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGC;IAAC,CAAC,GAACb,CAAC;IAACc,CAAC,GAACjB,CAAC,CAAC,CAAC;IAACkB,CAAC,GAACpB,CAAC,CAAC,CAAC;IAAC;MAACqB,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC1C,CAAC,CAAC;MAACgC,SAAS,EAACC;IAAC,CAAC,CAAC;IAAC;MAACU,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC5C,CAAC,CAAC;MAAC6C,UAAU,EAAChB;IAAC,CAAC,CAAC;IAACiB,CAAC,GAAClC,CAAC,CAAC;MAACmC,GAAG,EAACzB,CAAC;MAACI,EAAE,EAACC,CAAC;MAAC,iBAAiB,EAACQ,CAAC;MAAC,kBAAkB,EAACC,CAAC;MAAC,cAAc,EAACH,CAAC,GAAC,MAAM,GAAC,KAAK,CAAC;MAACL,QAAQ,EAACC,CAAC,IAAE,KAAK,CAAC;MAACC,SAAS,EAACC;IAAC,CAAC,EAACS,CAAC,EAACI,CAAC,CAAC;IAACI,CAAC,GAAC9C,CAAC,CAAC,OAAK;MAAC0B,QAAQ,EAACC,CAAC;MAACG,OAAO,EAACC,CAAC;MAACgB,KAAK,EAACP,CAAC;MAACQ,KAAK,EAACZ,CAAC;MAACa,SAAS,EAACpB;IAAC,CAAC,CAAC,EAAC,CAACF,CAAC,EAACI,CAAC,EAACS,CAAC,EAACJ,CAAC,EAACP,CAAC,CAAC,CAAC;EAAC,OAAOjB,CAAC,CAAC,CAAC,CAAC;IAACsC,QAAQ,EAACN,CAAC;IAACO,UAAU,EAACnB,CAAC;IAACoB,IAAI,EAACN,CAAC;IAACO,UAAU,EAACpC,CAAC;IAACqC,IAAI,EAAC;EAAU,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC/C,CAAC,CAACU,CAAC,CAAC;AAAC,SAAOqC,CAAC,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}