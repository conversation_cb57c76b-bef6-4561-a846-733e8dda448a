{"ast": null, "code": "import { disposables as u } from '../../utils/disposables.js';\nimport * as o from '../../utils/dom.js';\nimport { isIOS as p } from '../../utils/platform.js';\nfunction w() {\n  return p() ? {\n    before({\n      doc: n,\n      d: l,\n      meta: f\n    }) {\n      function i(a) {\n        return f.containers.flatMap(r => r()).some(r => r.contains(a));\n      }\n      l.microTask(() => {\n        var c;\n        if (window.getComputedStyle(n.documentElement).scrollBehavior !== \"auto\") {\n          let t = u();\n          t.style(n.documentElement, \"scrollBehavior\", \"auto\"), l.add(() => l.microTask(() => t.dispose()));\n        }\n        let a = (c = window.scrollY) != null ? c : window.pageYOffset,\n          r = null;\n        l.addEventListener(n, \"click\", t => {\n          if (o.isHTMLorSVGElement(t.target)) try {\n            let e = t.target.closest(\"a\");\n            if (!e) return;\n            let {\n                hash: m\n              } = new URL(e.href),\n              s = n.querySelector(m);\n            o.isHTMLorSVGElement(s) && !i(s) && (r = s);\n          } catch {}\n        }, !0), l.addEventListener(n, \"touchstart\", t => {\n          if (o.isHTMLorSVGElement(t.target) && o.hasInlineStyle(t.target)) if (i(t.target)) {\n            let e = t.target;\n            for (; e.parentElement && i(e.parentElement);) e = e.parentElement;\n            l.style(e, \"overscrollBehavior\", \"contain\");\n          } else l.style(t.target, \"touchAction\", \"none\");\n        }), l.addEventListener(n, \"touchmove\", t => {\n          if (o.isHTMLorSVGElement(t.target)) {\n            if (o.isHTMLInputElement(t.target)) return;\n            if (i(t.target)) {\n              let e = t.target;\n              for (; e.parentElement && e.dataset.headlessuiPortal !== \"\" && !(e.scrollHeight > e.clientHeight || e.scrollWidth > e.clientWidth);) e = e.parentElement;\n              e.dataset.headlessuiPortal === \"\" && t.preventDefault();\n            } else t.preventDefault();\n          }\n        }, {\n          passive: !1\n        }), l.add(() => {\n          var e;\n          let t = (e = window.scrollY) != null ? e : window.pageYOffset;\n          a !== t && window.scrollTo(0, a), r && r.isConnected && (r.scrollIntoView({\n            block: \"nearest\"\n          }), r = null);\n        });\n      });\n    }\n  } : {};\n}\nexport { w as handleIOSLocking };", "map": {"version": 3, "names": ["disposables", "u", "o", "isIOS", "p", "w", "before", "doc", "n", "d", "l", "meta", "f", "i", "a", "containers", "flatMap", "r", "some", "contains", "microTask", "c", "window", "getComputedStyle", "documentElement", "scroll<PERSON>eh<PERSON>or", "t", "style", "add", "dispose", "scrollY", "pageYOffset", "addEventListener", "isHTMLorSVGElement", "target", "e", "closest", "hash", "m", "URL", "href", "s", "querySelector", "hasInlineStyle", "parentElement", "isHTMLInputElement", "dataset", "headless<PERSON><PERSON><PERSON><PERSON>", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "preventDefault", "passive", "scrollTo", "isConnected", "scrollIntoView", "block", "handleIOSLocking"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js"], "sourcesContent": ["import{disposables as u}from'../../utils/disposables.js';import*as o from'../../utils/dom.js';import{isIOS as p}from'../../utils/platform.js';function w(){return p()?{before({doc:n,d:l,meta:f}){function i(a){return f.containers.flatMap(r=>r()).some(r=>r.contains(a))}l.microTask(()=>{var c;if(window.getComputedStyle(n.documentElement).scrollBehavior!==\"auto\"){let t=u();t.style(n.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(c=window.scrollY)!=null?c:window.pageYOffset,r=null;l.addEventListener(n,\"click\",t=>{if(o.isHTMLorSVGElement(t.target))try{let e=t.target.closest(\"a\");if(!e)return;let{hash:m}=new URL(e.href),s=n.querySelector(m);o.isHTMLorSVGElement(s)&&!i(s)&&(r=s)}catch{}},!0),l.addEventListener(n,\"touchstart\",t=>{if(o.isHTMLorSVGElement(t.target)&&o.hasInlineStyle(t.target))if(i(t.target)){let e=t.target;for(;e.parentElement&&i(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(n,\"touchmove\",t=>{if(o.isHTMLorSVGElement(t.target)){if(o.isHTMLInputElement(t.target))return;if(i(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),r&&r.isConnected&&(r.scrollIntoView({block:\"nearest\"}),r=null)})})}}:{}}export{w as handleIOSLocking};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,OAAM,KAAIC,CAAC,MAAK,oBAAoB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOD,CAAC,CAAC,CAAC,GAAC;IAACE,MAAMA,CAAC;MAACC,GAAG,EAACC,CAAC;MAACC,CAAC,EAACC,CAAC;MAACC,IAAI,EAACC;IAAC,CAAC,EAAC;MAAC,SAASC,CAACA,CAACC,CAAC,EAAC;QAAC,OAAOF,CAAC,CAACG,UAAU,CAACC,OAAO,CAACC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACD,CAAC,IAAEA,CAAC,CAACE,QAAQ,CAACL,CAAC,CAAC,CAAC;MAAA;MAACJ,CAAC,CAACU,SAAS,CAAC,MAAI;QAAC,IAAIC,CAAC;QAAC,IAAGC,MAAM,CAACC,gBAAgB,CAACf,CAAC,CAACgB,eAAe,CAAC,CAACC,cAAc,KAAG,MAAM,EAAC;UAAC,IAAIC,CAAC,GAACzB,CAAC,CAAC,CAAC;UAACyB,CAAC,CAACC,KAAK,CAACnB,CAAC,CAACgB,eAAe,EAAC,gBAAgB,EAAC,MAAM,CAAC,EAACd,CAAC,CAACkB,GAAG,CAAC,MAAIlB,CAAC,CAACU,SAAS,CAAC,MAAIM,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;QAAA;QAAC,IAAIf,CAAC,GAAC,CAACO,CAAC,GAACC,MAAM,CAACQ,OAAO,KAAG,IAAI,GAACT,CAAC,GAACC,MAAM,CAACS,WAAW;UAACd,CAAC,GAAC,IAAI;QAACP,CAAC,CAACsB,gBAAgB,CAACxB,CAAC,EAAC,OAAO,EAACkB,CAAC,IAAE;UAAC,IAAGxB,CAAC,CAAC+B,kBAAkB,CAACP,CAAC,CAACQ,MAAM,CAAC,EAAC,IAAG;YAAC,IAAIC,CAAC,GAACT,CAAC,CAACQ,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;YAAC,IAAG,CAACD,CAAC,EAAC;YAAO,IAAG;gBAACE,IAAI,EAACC;cAAC,CAAC,GAAC,IAAIC,GAAG,CAACJ,CAAC,CAACK,IAAI,CAAC;cAACC,CAAC,GAACjC,CAAC,CAACkC,aAAa,CAACJ,CAAC,CAAC;YAACpC,CAAC,CAAC+B,kBAAkB,CAACQ,CAAC,CAAC,IAAE,CAAC5B,CAAC,CAAC4B,CAAC,CAAC,KAAGxB,CAAC,GAACwB,CAAC,CAAC;UAAA,CAAC,OAAK,CAAC;QAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC/B,CAAC,CAACsB,gBAAgB,CAACxB,CAAC,EAAC,YAAY,EAACkB,CAAC,IAAE;UAAC,IAAGxB,CAAC,CAAC+B,kBAAkB,CAACP,CAAC,CAACQ,MAAM,CAAC,IAAEhC,CAAC,CAACyC,cAAc,CAACjB,CAAC,CAACQ,MAAM,CAAC,EAAC,IAAGrB,CAAC,CAACa,CAAC,CAACQ,MAAM,CAAC,EAAC;YAAC,IAAIC,CAAC,GAACT,CAAC,CAACQ,MAAM;YAAC,OAAKC,CAAC,CAACS,aAAa,IAAE/B,CAAC,CAACsB,CAAC,CAACS,aAAa,CAAC,GAAET,CAAC,GAACA,CAAC,CAACS,aAAa;YAAClC,CAAC,CAACiB,KAAK,CAACQ,CAAC,EAAC,oBAAoB,EAAC,SAAS,CAAC;UAAA,CAAC,MAAKzB,CAAC,CAACiB,KAAK,CAACD,CAAC,CAACQ,MAAM,EAAC,aAAa,EAAC,MAAM,CAAC;QAAA,CAAC,CAAC,EAACxB,CAAC,CAACsB,gBAAgB,CAACxB,CAAC,EAAC,WAAW,EAACkB,CAAC,IAAE;UAAC,IAAGxB,CAAC,CAAC+B,kBAAkB,CAACP,CAAC,CAACQ,MAAM,CAAC,EAAC;YAAC,IAAGhC,CAAC,CAAC2C,kBAAkB,CAACnB,CAAC,CAACQ,MAAM,CAAC,EAAC;YAAO,IAAGrB,CAAC,CAACa,CAAC,CAACQ,MAAM,CAAC,EAAC;cAAC,IAAIC,CAAC,GAACT,CAAC,CAACQ,MAAM;cAAC,OAAKC,CAAC,CAACS,aAAa,IAAET,CAAC,CAACW,OAAO,CAACC,gBAAgB,KAAG,EAAE,IAAE,EAAEZ,CAAC,CAACa,YAAY,GAACb,CAAC,CAACc,YAAY,IAAEd,CAAC,CAACe,WAAW,GAACf,CAAC,CAACgB,WAAW,CAAC,GAAEhB,CAAC,GAACA,CAAC,CAACS,aAAa;cAACT,CAAC,CAACW,OAAO,CAACC,gBAAgB,KAAG,EAAE,IAAErB,CAAC,CAAC0B,cAAc,CAAC,CAAC;YAAA,CAAC,MAAK1B,CAAC,CAAC0B,cAAc,CAAC,CAAC;UAAA;QAAC,CAAC,EAAC;UAACC,OAAO,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC3C,CAAC,CAACkB,GAAG,CAAC,MAAI;UAAC,IAAIO,CAAC;UAAC,IAAIT,CAAC,GAAC,CAACS,CAAC,GAACb,MAAM,CAACQ,OAAO,KAAG,IAAI,GAACK,CAAC,GAACb,MAAM,CAACS,WAAW;UAACjB,CAAC,KAAGY,CAAC,IAAEJ,MAAM,CAACgC,QAAQ,CAAC,CAAC,EAACxC,CAAC,CAAC,EAACG,CAAC,IAAEA,CAAC,CAACsC,WAAW,KAAGtC,CAAC,CAACuC,cAAc,CAAC;YAACC,KAAK,EAAC;UAAS,CAAC,CAAC,EAACxC,CAAC,GAAC,IAAI,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA;EAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAAOZ,CAAC,IAAIqD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}