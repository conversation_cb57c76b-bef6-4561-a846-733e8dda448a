{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\setting\\\\client\\\\src\\\\components\\\\settings\\\\ProfileSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfileSettings = ({\n  settings,\n  onUpdate\n}) => {\n  _s();\n  var _formData$socialLinks, _formData$socialLinks2, _formData$socialLinks3;\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pb-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Profile Photo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shrink-0\",\n            children: formData.avatar ? /*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"h-20 w-20 rounded-full object-cover\",\n              src: formData.avatar,\n              alt: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(UserCircleIcon, {\n              className: \"h-20 w-20 text-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(PhotoIcon, {\n                className: \"h-4 w-4 inline mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), \"Change Photo\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-gray-500\",\n              children: \"JPG, GIF or PNG. 1MB max.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pb-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Personal Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"firstName\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"firstName\",\n              id: \"firstName\",\n              value: formData.firstName || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"lastName\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"lastName\",\n              id: \"lastName\",\n              value: formData.lastName || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              id: \"email\",\n              value: formData.email || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              name: \"phone\",\n              id: \"phone\",\n              value: formData.phone || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"bio\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Bio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"bio\",\n              id: \"bio\",\n              rows: 3,\n              value: formData.bio || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              placeholder: \"Tell us about yourself...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"location\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"location\",\n              id: \"location\",\n              value: formData.location || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"website\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Website\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              name: \"website\",\n              id: \"website\",\n              value: formData.website || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Social Links\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"socialLinks.twitter\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Twitter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"socialLinks.twitter\",\n              id: \"socialLinks.twitter\",\n              value: ((_formData$socialLinks = formData.socialLinks) === null || _formData$socialLinks === void 0 ? void 0 : _formData$socialLinks.twitter) || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              placeholder: \"@username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"socialLinks.linkedin\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"LinkedIn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"socialLinks.linkedin\",\n              id: \"socialLinks.linkedin\",\n              value: ((_formData$socialLinks2 = formData.socialLinks) === null || _formData$socialLinks2 === void 0 ? void 0 : _formData$socialLinks2.linkedin) || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              placeholder: \"linkedin.com/in/username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"socialLinks.facebook\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"socialLinks.facebook\",\n              id: \"socialLinks.facebook\",\n              value: ((_formData$socialLinks3 = formData.socialLinks) === null || _formData$socialLinks3 === void 0 ? void 0 : _formData$socialLinks3.facebook) || '',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              placeholder: \"facebook.com/username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setIsEditing(true),\n          className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Edit Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileSettings, \"rEZs2nirDlaVd6Cwh1oU/4Iupv4=\");\n_c = ProfileSettings;\nexport default ProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"ProfileSettings\");", "map": {"version": 3, "names": ["React", "useState", "PhotoIcon", "UserCircleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileSettings", "settings", "onUpdate", "_s", "_formData$socialLinks", "_formData$socialLinks2", "_formData$socialLinks3", "formData", "setFormData", "isEditing", "setIsEditing", "handleInputChange", "e", "name", "value", "target", "includes", "parent", "child", "split", "prev", "handleSubmit", "preventDefault", "handleCancel", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "avatar", "src", "alt", "type", "htmlFor", "id", "firstName", "onChange", "disabled", "lastName", "email", "phone", "rows", "bio", "placeholder", "location", "website", "socialLinks", "twitter", "linkedin", "facebook", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/src/components/settings/ProfileSettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid';\n\nconst ProfileSettings = ({ settings, onUpdate }) => {\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <form onSubmit={handleSubmit}>\n        {/* Avatar Section */}\n        <div className=\"pb-6 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Photo</h3>\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"shrink-0\">\n              {formData.avatar ? (\n                <img\n                  className=\"h-20 w-20 rounded-full object-cover\"\n                  src={formData.avatar}\n                  alt=\"Profile\"\n                />\n              ) : (\n                <UserCircleIcon className=\"h-20 w-20 text-gray-300\" />\n              )}\n            </div>\n            <div>\n              <button\n                type=\"button\"\n                className=\"bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <PhotoIcon className=\"h-4 w-4 inline mr-2\" />\n                Change Photo\n              </button>\n              <p className=\"mt-2 text-sm text-gray-500\">\n                JPG, GIF or PNG. 1MB max.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Personal Information */}\n        <div className=\"pb-6 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Personal Information</h3>\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700\">\n                First Name\n              </label>\n              <input\n                type=\"text\"\n                name=\"firstName\"\n                id=\"firstName\"\n                value={formData.firstName || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700\">\n                Last Name\n              </label>\n              <input\n                type=\"text\"\n                name=\"lastName\"\n                id=\"lastName\"\n                value={formData.lastName || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                type=\"email\"\n                name=\"email\"\n                id=\"email\"\n                value={formData.email || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                name=\"phone\"\n                id=\"phone\"\n                value={formData.phone || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div className=\"sm:col-span-2\">\n              <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700\">\n                Bio\n              </label>\n              <textarea\n                name=\"bio\"\n                id=\"bio\"\n                rows={3}\n                value={formData.bio || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n                placeholder=\"Tell us about yourself...\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700\">\n                Location\n              </label>\n              <input\n                type=\"text\"\n                name=\"location\"\n                id=\"location\"\n                value={formData.location || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700\">\n                Website\n              </label>\n              <input\n                type=\"url\"\n                name=\"website\"\n                id=\"website\"\n                value={formData.website || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Social Links */}\n        <div className=\"pb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Social Links</h3>\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label htmlFor=\"socialLinks.twitter\" className=\"block text-sm font-medium text-gray-700\">\n                Twitter\n              </label>\n              <input\n                type=\"text\"\n                name=\"socialLinks.twitter\"\n                id=\"socialLinks.twitter\"\n                value={formData.socialLinks?.twitter || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n                placeholder=\"@username\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"socialLinks.linkedin\" className=\"block text-sm font-medium text-gray-700\">\n                LinkedIn\n              </label>\n              <input\n                type=\"text\"\n                name=\"socialLinks.linkedin\"\n                id=\"socialLinks.linkedin\"\n                value={formData.socialLinks?.linkedin || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n                placeholder=\"linkedin.com/in/username\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"socialLinks.facebook\" className=\"block text-sm font-medium text-gray-700\">\n                Facebook\n              </label>\n              <input\n                type=\"text\"\n                name=\"socialLinks.facebook\"\n                id=\"socialLinks.facebook\"\n                value={formData.socialLinks?.facebook || ''}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n                placeholder=\"facebook.com/username\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3\">\n          {isEditing ? (\n            <>\n              <button\n                type=\"button\"\n                onClick={handleCancel}\n                className=\"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Save Changes\n              </button>\n            </>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={() => setIsEditing(true)}\n              className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Edit Profile\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ProfileSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,cAAc,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAACQ,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMkB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,IAAIF,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACM,KAAK,CAAC,GAAG,CAAC;MACvCX,WAAW,CAACY,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACH,MAAM,GAAG;UACR,GAAGG,IAAI,CAACH,MAAM,CAAC;UACf,CAACC,KAAK,GAAGJ;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLN,WAAW,CAACY,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACP,IAAI,GAAGC;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMO,YAAY,GAAIT,CAAC,IAAK;IAC1BA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBpB,QAAQ,CAACK,QAAQ,CAAC;IAClBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBf,WAAW,CAACP,QAAQ,CAAC;IACrBS,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEb,OAAA;IAAK2B,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxB5B,OAAA;MAAM6B,QAAQ,EAAEL,YAAa;MAAAI,QAAA,gBAE3B5B,OAAA;QAAK2B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5C5B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEjC,OAAA;UAAK2B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5B,OAAA;YAAK2B,SAAS,EAAC,UAAU;YAAAC,QAAA,EACtBlB,QAAQ,CAACwB,MAAM,gBACdlC,OAAA;cACE2B,SAAS,EAAC,qCAAqC;cAC/CQ,GAAG,EAAEzB,QAAQ,CAACwB,MAAO;cACrBE,GAAG,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,gBAEFjC,OAAA,CAACF,cAAc;cAAC6B,SAAS,EAAC;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACtD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cACEqC,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,qMAAqM;cAAAC,QAAA,gBAE/M5B,OAAA,CAACH,SAAS;gBAAC8B,SAAS,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjC,OAAA;cAAG2B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK2B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5C5B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFjC,OAAA;UAAK2B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5B,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,WAAW;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE/E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,WAAW;cAChBuB,EAAE,EAAC,WAAW;cACdtB,KAAK,EAAEP,QAAQ,CAAC8B,SAAS,IAAI,EAAG;cAChCC,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,UAAU;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,UAAU;cACfuB,EAAE,EAAC,UAAU;cACbtB,KAAK,EAAEP,QAAQ,CAACiC,QAAQ,IAAI,EAAG;cAC/BF,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,OAAO;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,OAAO;cACZrB,IAAI,EAAC,OAAO;cACZuB,EAAE,EAAC,OAAO;cACVtB,KAAK,EAAEP,QAAQ,CAACkC,KAAK,IAAI,EAAG;cAC5BH,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,OAAO;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,KAAK;cACVrB,IAAI,EAAC,OAAO;cACZuB,EAAE,EAAC,OAAO;cACVtB,KAAK,EAAEP,QAAQ,CAACmC,KAAK,IAAI,EAAG;cAC5BJ,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5B,OAAA;cAAOsC,OAAO,EAAC,KAAK;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEzE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEgB,IAAI,EAAC,KAAK;cACVuB,EAAE,EAAC,KAAK;cACRO,IAAI,EAAE,CAAE;cACR7B,KAAK,EAAEP,QAAQ,CAACqC,GAAG,IAAI,EAAG;cAC1BN,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC,wJAAwJ;cAClKqB,WAAW,EAAC;YAA2B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,UAAU;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,UAAU;cACfuB,EAAE,EAAC,UAAU;cACbtB,KAAK,EAAEP,QAAQ,CAACuC,QAAQ,IAAI,EAAG;cAC/BR,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,SAAS;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE7E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,KAAK;cACVrB,IAAI,EAAC,SAAS;cACduB,EAAE,EAAC,SAAS;cACZtB,KAAK,EAAEP,QAAQ,CAACwC,OAAO,IAAI,EAAG;cAC9BT,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC;YAAwJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB5B,OAAA;UAAI2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEjC,OAAA;UAAK2B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5B,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,qBAAqB;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEzF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,qBAAqB;cAC1BuB,EAAE,EAAC,qBAAqB;cACxBtB,KAAK,EAAE,EAAAV,qBAAA,GAAAG,QAAQ,CAACyC,WAAW,cAAA5C,qBAAA,uBAApBA,qBAAA,CAAsB6C,OAAO,KAAI,EAAG;cAC3CX,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC,wJAAwJ;cAClKqB,WAAW,EAAC;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,sBAAsB;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1F;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,sBAAsB;cAC3BuB,EAAE,EAAC,sBAAsB;cACzBtB,KAAK,EAAE,EAAAT,sBAAA,GAAAE,QAAQ,CAACyC,WAAW,cAAA3C,sBAAA,uBAApBA,sBAAA,CAAsB6C,QAAQ,KAAI,EAAG;cAC5CZ,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC,wJAAwJ;cAClKqB,WAAW,EAAC;YAA0B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA4B,QAAA,gBACE5B,OAAA;cAAOsC,OAAO,EAAC,sBAAsB;cAACX,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1F;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA;cACEqC,IAAI,EAAC,MAAM;cACXrB,IAAI,EAAC,sBAAsB;cAC3BuB,EAAE,EAAC,sBAAsB;cACzBtB,KAAK,EAAE,EAAAR,sBAAA,GAAAC,QAAQ,CAACyC,WAAW,cAAA1C,sBAAA,uBAApBA,sBAAA,CAAsB6C,QAAQ,KAAI,EAAG;cAC5Cb,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBe,SAAS,EAAC,wJAAwJ;cAClKqB,WAAW,EAAC;YAAuB;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK2B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxChB,SAAS,gBACRZ,OAAA,CAAAE,SAAA;UAAA0B,QAAA,gBACE5B,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbkB,OAAO,EAAE7B,YAAa;YACtBC,SAAS,EAAC,2LAA2L;YAAAC,QAAA,EACtM;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjC,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbV,SAAS,EAAC,+LAA+L;YAAAC,QAAA,EAC1M;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHjC,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACbkB,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAAC,IAAI,CAAE;UAClCc,SAAS,EAAC,+LAA+L;UAAAC,QAAA,EAC1M;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAxQIH,eAAe;AAAAqD,EAAA,GAAfrD,eAAe;AA0QrB,eAAeA,eAAe;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}