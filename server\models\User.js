const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  fullName: String,
  email: { type: String, unique: true },
  phone: String,
  avatar: String,
  role: { type: String, default: "user" },

  // Security fields
  password: String,
  twoFactorAuth: { type: Boolean, default: false },
  loginNotifications: { type: Boolean, default: true },
  sessionTimeout: { type: Number, default: 30 }, // minutes
  passwordLastChanged: { type: Date, default: Date.now },

  // Additional profile fields
  bio: String,
  location: String,
  website: String,
  socialLinks: {
    twitter: String,
    linkedin: String,
    facebook: String
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('User', UserSchema);
