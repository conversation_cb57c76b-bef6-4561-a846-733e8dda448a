{"ast": null, "code": "import { useEffect as T, useRef as E } from \"react\";\nimport { getOwnerDocument as d } from '../utils/owner.js';\nimport { useIsoMorphicEffect as N } from './use-iso-morphic-effect.js';\nfunction F(c, {\n  container: e,\n  accept: t,\n  walk: r\n}) {\n  let o = E(t),\n    l = E(r);\n  T(() => {\n    o.current = t, l.current = r;\n  }, [t, r]), N(() => {\n    if (!e || !c) return;\n    let n = d(e);\n    if (!n) return;\n    let f = o.current,\n      p = l.current,\n      i = Object.assign(m => f(m), {\n        acceptNode: f\n      }),\n      u = n.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, i, !1);\n    for (; u.nextNode();) p(u.currentNode);\n  }, [e, c, o, l]);\n}\nexport { F as useTreeWalker };", "map": {"version": 3, "names": ["useEffect", "T", "useRef", "E", "getOwnerDocument", "d", "useIsoMorphicEffect", "N", "F", "c", "container", "e", "accept", "t", "walk", "r", "o", "l", "current", "n", "f", "p", "i", "Object", "assign", "m", "acceptNode", "u", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "nextNode", "currentNode", "useTreeWalker"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-tree-walker.js"], "sourcesContent": ["import{useEffect as T,useRef as E}from\"react\";import{getOwnerDocument as d}from'../utils/owner.js';import{useIsoMorphicEffect as N}from'./use-iso-morphic-effect.js';function F(c,{container:e,accept:t,walk:r}){let o=E(t),l=E(r);T(()=>{o.current=t,l.current=r},[t,r]),N(()=>{if(!e||!c)return;let n=d(e);if(!n)return;let f=o.current,p=l.current,i=Object.assign(m=>f(m),{acceptNode:f}),u=n.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,i,!1);for(;u.nextNode();)p(u.currentNode)},[e,c,o,l])}export{F as useTreeWalker};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAACC,SAAS,EAACC,CAAC;EAACC,MAAM,EAACC,CAAC;EAACC,IAAI,EAACC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACb,CAAC,CAACU,CAAC,CAAC;IAACI,CAAC,GAACd,CAAC,CAACY,CAAC,CAAC;EAACd,CAAC,CAAC,MAAI;IAACe,CAAC,CAACE,OAAO,GAACL,CAAC,EAACI,CAAC,CAACC,OAAO,GAACH,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,CAAC,CAAC,EAACR,CAAC,CAAC,MAAI;IAAC,IAAG,CAACI,CAAC,IAAE,CAACF,CAAC,EAAC;IAAO,IAAIU,CAAC,GAACd,CAAC,CAACM,CAAC,CAAC;IAAC,IAAG,CAACQ,CAAC,EAAC;IAAO,IAAIC,CAAC,GAACJ,CAAC,CAACE,OAAO;MAACG,CAAC,GAACJ,CAAC,CAACC,OAAO;MAACI,CAAC,GAACC,MAAM,CAACC,MAAM,CAACC,CAAC,IAAEL,CAAC,CAACK,CAAC,CAAC,EAAC;QAACC,UAAU,EAACN;MAAC,CAAC,CAAC;MAACO,CAAC,GAACR,CAAC,CAACS,gBAAgB,CAACjB,CAAC,EAACkB,UAAU,CAACC,YAAY,EAACR,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,OAAKK,CAAC,CAACI,QAAQ,CAAC,CAAC,GAAEV,CAAC,CAACM,CAAC,CAACK,WAAW,CAAC;EAAA,CAAC,EAAC,CAACrB,CAAC,EAACF,CAAC,EAACO,CAAC,EAACC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOT,CAAC,IAAIyB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}