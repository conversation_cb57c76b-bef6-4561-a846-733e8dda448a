const bcrypt = require('bcryptjs');
const User = require('../models/User');

// PUT change password
const changePassword = async (req, res) => {
  try {
    const { userId, currentPassword, newPassword } = req.body;
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }
    
    // Verify current password (assuming password field exists)
    // Note: In real implementation, you'd have a password field in User model
    const isValidPassword = await bcrypt.compare(currentPassword, user.password || '');
    if (!isValidPassword) {
      return res.status(400).json({ 
        success: false, 
        message: 'Current password is incorrect' 
      });
    }
    
    // Hash new password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update password
    await User.findByIdAndUpdate(userId, { 
      password: hashedPassword,
      passwordLastChanged: new Date()
    });
    
    res.json({ 
      success: true, 
      message: 'Password changed successfully' 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// PUT toggle 2FA
const toggle2FA = async (req, res) => {
  try {
    const { userId, enable2FA } = req.body;
    
    const user = await User.findByIdAndUpdate(
      userId, 
      { twoFactorAuth: enable2FA },
      { new: true }
    );
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }
    
    res.json({ 
      success: true, 
      message: `2FA ${enable2FA ? 'enabled' : 'disabled'} successfully`,
      data: { twoFactorAuth: user.twoFactorAuth }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// GET security settings
const getSecuritySettings = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(userId).select('twoFactorAuth passwordLastChanged');
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }
    
    res.json({ 
      success: true, 
      data: {
        twoFactorAuth: user.twoFactorAuth || false,
        passwordLastChanged: user.passwordLastChanged || new Date(),
        loginNotifications: true, // Default value
        sessionTimeout: 30 // Default value in minutes
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// PUT update security settings
const updateSecuritySettings = async (req, res) => {
  try {
    const { userId } = req.params;
    const { twoFactorAuth, loginNotifications, sessionTimeout } = req.body;
    
    const user = await User.findByIdAndUpdate(
      userId, 
      { 
        twoFactorAuth,
        loginNotifications,
        sessionTimeout
      },
      { new: true }
    );
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }
    
    res.json({ 
      success: true, 
      message: 'Security settings updated successfully',
      data: {
        twoFactorAuth: user.twoFactorAuth,
        loginNotifications: user.loginNotifications,
        sessionTimeout: user.sessionTimeout
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

module.exports = {
  changePassword,
  toggle2FA,
  getSecuritySettings,
  updateSecuritySettings
};
