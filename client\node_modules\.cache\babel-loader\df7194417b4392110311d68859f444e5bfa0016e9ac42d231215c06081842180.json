{"ast": null, "code": "function _class_extract_field_descriptor(receiver, privateMap, action) {\n  if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n  return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };", "map": {"version": 3, "names": ["_class_extract_field_descriptor", "receiver", "privateMap", "action", "has", "TypeError", "get", "_"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAE;EACnE,IAAI,CAACD,UAAU,CAACE,GAAG,CAACH,QAAQ,CAAC,EAAE,MAAM,IAAII,SAAS,CAAC,eAAe,GAAGF,MAAM,GAAG,gCAAgC,CAAC;EAE/G,OAAOD,UAAU,CAACI,GAAG,CAACL,QAAQ,CAAC;AACnC;AACA,SAASD,+BAA+B,IAAIO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}