{"name": "event-management-system", "version": "1.0.0", "description": "Event Management System with Settings Dashboard", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd client && npm run build", "start": "cd server && npm start"}, "keywords": ["event-management", "settings", "dashboard", "react", "nodejs"], "author": "Event Management Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}