{"ast": null, "code": "var a = Object.defineProperty;\nvar r = (e, c, t) => c in e ? a(e, c, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: t\n}) : e[c] = t;\nvar p = (e, c, t) => (r(e, typeof c != \"symbol\" ? c + \"\" : c, t), t);\nimport { Machine as d } from '../machine.js';\nimport { DefaultMap as l } from '../utils/default-map.js';\nimport { match as u } from '../utils/match.js';\nvar k = (t => (t[t.Push = 0] = \"Push\", t[t.Pop = 1] = \"Pop\", t))(k || {});\nlet y = {\n  [0](e, c) {\n    let t = c.id,\n      s = e.stack,\n      i = e.stack.indexOf(t);\n    if (i !== -1) {\n      let n = e.stack.slice();\n      return n.splice(i, 1), n.push(t), s = n, {\n        ...e,\n        stack: s\n      };\n    }\n    return {\n      ...e,\n      stack: [...e.stack, t]\n    };\n  },\n  [1](e, c) {\n    let t = c.id,\n      s = e.stack.indexOf(t);\n    if (s === -1) return e;\n    let i = e.stack.slice();\n    return i.splice(s, 1), {\n      ...e,\n      stack: i\n    };\n  }\n};\nclass o extends d {\n  constructor() {\n    super(...arguments);\n    p(this, \"actions\", {\n      push: t => this.send({\n        type: 0,\n        id: t\n      }),\n      pop: t => this.send({\n        type: 1,\n        id: t\n      })\n    });\n    p(this, \"selectors\", {\n      isTop: (t, s) => t.stack[t.stack.length - 1] === s,\n      inStack: (t, s) => t.stack.includes(s)\n    });\n  }\n  static new() {\n    return new o({\n      stack: []\n    });\n  }\n  reduce(t, s) {\n    return u(s.type, y, t, s);\n  }\n}\nconst x = new l(() => o.new());\nexport { k as ActionTypes, x as stackMachines };", "map": {"version": 3, "names": ["a", "Object", "defineProperty", "r", "e", "c", "t", "enumerable", "configurable", "writable", "value", "p", "Machine", "d", "DefaultMap", "l", "match", "u", "k", "<PERSON><PERSON>", "Pop", "y", "id", "s", "stack", "i", "indexOf", "n", "slice", "splice", "push", "o", "constructor", "arguments", "send", "type", "pop", "isTop", "length", "inStack", "includes", "new", "reduce", "x", "ActionTypes", "stackMachines"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/machines/stack-machine.js"], "sourcesContent": ["var a=Object.defineProperty;var r=(e,c,t)=>c in e?a(e,c,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[c]=t;var p=(e,c,t)=>(r(e,typeof c!=\"symbol\"?c+\"\":c,t),t);import{Machine as d}from'../machine.js';import{DefaultMap as l}from'../utils/default-map.js';import{match as u}from'../utils/match.js';var k=(t=>(t[t.Push=0]=\"Push\",t[t.Pop=1]=\"Pop\",t))(k||{});let y={[0](e,c){let t=c.id,s=e.stack,i=e.stack.indexOf(t);if(i!==-1){let n=e.stack.slice();return n.splice(i,1),n.push(t),s=n,{...e,stack:s}}return{...e,stack:[...e.stack,t]}},[1](e,c){let t=c.id,s=e.stack.indexOf(t);if(s===-1)return e;let i=e.stack.slice();return i.splice(s,1),{...e,stack:i}}};class o extends d{constructor(){super(...arguments);p(this,\"actions\",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});p(this,\"selectors\",{isTop:(t,s)=>t.stack[t.stack.length-1]===s,inStack:(t,s)=>t.stack.includes(s)})}static new(){return new o({stack:[]})}reduce(t,s){return u(s.type,y,t,s)}}const x=new l(()=>o.new());export{k as ActionTypes,x as stackMachines};\n"], "mappings": "AAAA,IAAIA,CAAC,GAACC,MAAM,CAACC,cAAc;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,IAAID,CAAC,GAACJ,CAAC,CAACI,CAAC,EAACC,CAAC,EAAC;EAACE,UAAU,EAAC,CAAC,CAAC;EAACC,YAAY,EAAC,CAAC,CAAC;EAACC,QAAQ,EAAC,CAAC,CAAC;EAACC,KAAK,EAACJ;AAAC,CAAC,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC;AAAC,IAAIK,CAAC,GAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIH,CAAC,CAACC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,EAAE,GAACA,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,SAAOM,OAAO,IAAIC,CAAC,QAAK,eAAe;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAAC,CAACZ,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACa,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACb,CAAC,CAACA,CAAC,CAACc,GAAG,GAAC,CAAC,CAAC,GAAC,KAAK,EAACd,CAAC,CAAC,EAAEY,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,IAAIG,CAAC,GAAC;EAAC,CAAC,CAAC,EAAEjB,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACiB,EAAE;MAACC,CAAC,GAACnB,CAAC,CAACoB,KAAK;MAACC,CAAC,GAACrB,CAAC,CAACoB,KAAK,CAACE,OAAO,CAACpB,CAAC,CAAC;IAAC,IAAGmB,CAAC,KAAG,CAAC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACvB,CAAC,CAACoB,KAAK,CAACI,KAAK,CAAC,CAAC;MAAC,OAAOD,CAAC,CAACE,MAAM,CAACJ,CAAC,EAAC,CAAC,CAAC,EAACE,CAAC,CAACG,IAAI,CAACxB,CAAC,CAAC,EAACiB,CAAC,GAACI,CAAC,EAAC;QAAC,GAAGvB,CAAC;QAACoB,KAAK,EAACD;MAAC,CAAC;IAAA;IAAC,OAAM;MAAC,GAAGnB,CAAC;MAACoB,KAAK,EAAC,CAAC,GAAGpB,CAAC,CAACoB,KAAK,EAAClB,CAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAEF,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACiB,EAAE;MAACC,CAAC,GAACnB,CAAC,CAACoB,KAAK,CAACE,OAAO,CAACpB,CAAC,CAAC;IAAC,IAAGiB,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOnB,CAAC;IAAC,IAAIqB,CAAC,GAACrB,CAAC,CAACoB,KAAK,CAACI,KAAK,CAAC,CAAC;IAAC,OAAOH,CAAC,CAACI,MAAM,CAACN,CAAC,EAAC,CAAC,CAAC,EAAC;MAAC,GAAGnB,CAAC;MAACoB,KAAK,EAACC;IAAC,CAAC;EAAA;AAAC,CAAC;AAAC,MAAMM,CAAC,SAASlB,CAAC;EAACmB,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,GAAGC,SAAS,CAAC;IAACtB,CAAC,CAAC,IAAI,EAAC,SAAS,EAAC;MAACmB,IAAI,EAACxB,CAAC,IAAE,IAAI,CAAC4B,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACb,EAAE,EAAChB;MAAC,CAAC,CAAC;MAAC8B,GAAG,EAAC9B,CAAC,IAAE,IAAI,CAAC4B,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACb,EAAE,EAAChB;MAAC,CAAC;IAAC,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,WAAW,EAAC;MAAC0B,KAAK,EAACA,CAAC/B,CAAC,EAACiB,CAAC,KAAGjB,CAAC,CAACkB,KAAK,CAAClB,CAAC,CAACkB,KAAK,CAACc,MAAM,GAAC,CAAC,CAAC,KAAGf,CAAC;MAACgB,OAAO,EAACA,CAACjC,CAAC,EAACiB,CAAC,KAAGjB,CAAC,CAACkB,KAAK,CAACgB,QAAQ,CAACjB,CAAC;IAAC,CAAC,CAAC;EAAA;EAAC,OAAOkB,GAAGA,CAAA,EAAE;IAAC,OAAO,IAAIV,CAAC,CAAC;MAACP,KAAK,EAAC;IAAE,CAAC,CAAC;EAAA;EAACkB,MAAMA,CAACpC,CAAC,EAACiB,CAAC,EAAC;IAAC,OAAON,CAAC,CAACM,CAAC,CAACY,IAAI,EAACd,CAAC,EAACf,CAAC,EAACiB,CAAC,CAAC;EAAA;AAAC;AAAC,MAAMoB,CAAC,GAAC,IAAI5B,CAAC,CAAC,MAAIgB,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC;AAAC,SAAOvB,CAAC,IAAI0B,WAAW,EAACD,CAAC,IAAIE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}