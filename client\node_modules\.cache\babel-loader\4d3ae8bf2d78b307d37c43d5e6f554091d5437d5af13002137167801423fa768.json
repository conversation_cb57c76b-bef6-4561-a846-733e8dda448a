{"ast": null, "code": "import { useEffect as l, useRef as i } from \"react\";\nimport { useEvent as r } from './use-event.js';\nlet u = Symbol();\nfunction T(t, n = !0) {\n  return Object.assign(t, {\n    [u]: n\n  });\n}\nfunction y(...t) {\n  let n = i(t);\n  l(() => {\n    n.current = t;\n  }, [t]);\n  let c = r(e => {\n    for (let o of n.current) o != null && (typeof o == \"function\" ? o(e) : o.current = e);\n  });\n  return t.every(e => e == null || (e == null ? void 0 : e[u])) ? void 0 : c;\n}\nexport { T as optionalRef, y as useSyncRefs };", "map": {"version": 3, "names": ["useEffect", "l", "useRef", "i", "useEvent", "r", "u", "Symbol", "T", "t", "n", "Object", "assign", "y", "current", "c", "e", "o", "every", "optionalRef", "useSyncRefs"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,IAAIC,CAAC,GAACC,MAAM,CAAC,CAAC;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,OAAOC,MAAM,CAACC,MAAM,CAACH,CAAC,EAAC;IAAC,CAACH,CAAC,GAAEI;EAAC,CAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAAC,GAAGJ,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACP,CAAC,CAACM,CAAC,CAAC;EAACR,CAAC,CAAC,MAAI;IAACS,CAAC,CAACI,OAAO,GAACL,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,IAAIM,CAAC,GAACV,CAAC,CAACW,CAAC,IAAE;IAAC,KAAI,IAAIC,CAAC,IAAIP,CAAC,CAACI,OAAO,EAACG,CAAC,IAAE,IAAI,KAAG,OAAOA,CAAC,IAAE,UAAU,GAACA,CAAC,CAACD,CAAC,CAAC,GAACC,CAAC,CAACH,OAAO,GAACE,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,OAAOP,CAAC,CAACS,KAAK,CAACF,CAAC,IAAEA,CAAC,IAAE,IAAI,KAAGA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACV,CAAC,CAAC,CAAC,CAAC,GAAC,KAAK,CAAC,GAACS,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIW,WAAW,EAACN,CAAC,IAAIO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}