# Event Management System - Settings Dashboard

A comprehensive Event Management System with a focus on the Settings section, featuring a modern tabbed interface for managing user preferences, notifications, events, profit tracking, security, and appearance.

## 🔧 Frontend Tabs in UI

- **Profile** - User profile management with avatar upload
- **Notifications** - Email, SMS, and push notification preferences
- **Events** - Event creation and management settings
- **Profit** - Revenue tracking and financial settings
- **Security** - Password, 2FA, and security preferences
- **Appearance** - Theme, layout, and UI customization

## ✅ Backend Functionality Design (Node.js + Express + MongoDB)

### Project Structure

```
backend/
├── controllers/
│   ├── userController.js
│   ├── notificationController.js
│   ├── eventController.js
│   ├── profitController.js
│   ├── authController.js
│   └── preferenceController.js
├── models/
│   ├── User.js
│   ├── Notification.js
│   ├── Event.js
│   ├── Profit.js
│   └── Preference.js
├── routes/
│   └── settings.js
├── index.js
└── .env
```

### Models

#### 1. User.js
```javascript
const UserSchema = new mongoose.Schema({
  fullName: String,
  email: { type: String, unique: true },
  phone: String,
  avatar: String,
  role: { type: String, default: "user" },
  // Security fields
  password: String,
  twoFactorAuth: { type: Boolean, default: false },
  loginNotifications: { type: Boolean, default: true },
  sessionTimeout: { type: Number, default: 30 },
  passwordLastChanged: { type: Date, default: Date.now }
});
```

#### 2. Notification.js
```javascript
const NotificationSchema = new mongoose.Schema({
  userId: mongoose.Schema.Types.ObjectId,
  message: String,
  read: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now }
});
```

#### 3. Event.js
```javascript
const EventSchema = new mongoose.Schema({
  title: String,
  description: String,
  date: Date,
  location: String,
  createdBy: mongoose.Schema.Types.ObjectId
});
```

#### 4. Profit.js
```javascript
const ProfitSchema = new mongoose.Schema({
  eventId: mongoose.Schema.Types.ObjectId,
  amount: Number,
  date: { type: Date, default: Date.now }
});
```

#### 5. Preference.js
```javascript
const PreferenceSchema = new mongoose.Schema({
  userId: mongoose.Schema.Types.ObjectId,
  theme: { type: String, enum: ['light', 'dark'], default: 'light' },
  language: String
});
```

## API Endpoints

### Profile Endpoints
- `GET /api/profile/:id` - Get user profile
- `PUT /api/profile/:id` - Update user profile
- `POST /api/profile/:id/avatar` - Upload avatar

### Notification Endpoints
- `GET /api/notifications/:userId` - Get user notifications
- `PUT /api/notifications/:id/mark-read` - Mark notification as read
- `POST /api/notifications` - Create notification

### Event Endpoints
- `GET /api/events` - Get all events
- `POST /api/events` - Create event
- `PUT /api/events/:id` - Update event
- `DELETE /api/events/:id` - Delete event

### Profit Endpoints
- `GET /api/profit/event/:eventId` - Get profit by event
- `POST /api/profit` - Record profit
- `GET /api/profit/total` - Get total profit
- `GET /api/profit/analytics` - Get profit analytics

### Security Endpoints
- `PUT /api/security/change-password` - Change password
- `PUT /api/security/2fa` - Toggle 2FA
- `GET /api/security/:userId` - Get security settings
- `PUT /api/security/:userId` - Update security settings

### Appearance/Preference Endpoints
- `GET /api/preferences/:userId` - Get user preferences
- `PUT /api/preferences/:userId` - Update preferences
- `GET /api/preferences/themes/available` - Get available themes
- `GET /api/preferences/languages/available` - Get available languages

## Installation & Setup

1. **Install dependencies:**
```bash
# Root directory
npm install

# Backend
cd server && npm install

# Frontend
cd client && npm install
```

2. **Environment Variables:**
```bash
# server/.env
PORT=5001
MONGODB_URI=mongodb://localhost:27017/event-management-settings
JWT_SECRET=your-super-secret-jwt-key
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
NODE_ENV=development
```

3. **Start the servers:**
```bash
# Backend (from server directory)
npm run dev

# Frontend (from client directory)
npm start
```

## Running the Application

- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5001

## Technologies Used

- **Frontend**: React, Custom CSS (Tailwind-like utilities)
- **Backend**: Node.js, Express.js, MongoDB, Mongoose
- **Authentication**: bcrypt for password hashing
- **File Upload**: Multer for avatar uploads
- **Development**: Nodemon for auto-restart

## Features

✅ **Complete Settings Dashboard** with 6 main sections
✅ **RESTful API** with proper MVC architecture
✅ **MongoDB Integration** with Mongoose ODM
✅ **File Upload** functionality for avatars
✅ **Security Features** including password management and 2FA
✅ **Responsive Design** with custom CSS utilities
✅ **Mock Data** for demonstration purposes

## License

MIT License
