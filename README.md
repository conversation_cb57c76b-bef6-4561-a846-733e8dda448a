# Event Management System - Settings Dashboard

A comprehensive Event Management System with a focus on the Settings section, featuring a modern tabbed interface for managing user preferences, notifications, events, profit tracking, security, and appearance.

## Features

### Frontend (React)
- **Profile Settings**: User profile management with avatar upload
- **Notifications**: Email, SMS, and push notification preferences
- **Events**: Event creation and management settings
- **Profit**: Revenue tracking and financial settings
- **Security**: Password, 2FA, and security preferences
- **Appearance**: Theme, layout, and UI customization

### Backend (Node.js + Express + MongoDB)
- RESTful API for settings management
- User authentication and authorization
- MongoDB database integration
- File upload handling for avatars
- Email notification system
- Security features (password hashing, JWT tokens)

## Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom hooks
│   │   ├── services/      # API services
│   │   └── styles/        # CSS/styling files
├── server/                # Node.js backend
│   ├── controllers/       # Route controllers
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   └── utils/            # Utility functions
└── package.json          # Root package.json
```

## Installation

1. Install all dependencies:
```bash
npm run install-all
```

2. Set up environment variables:
```bash
# In server/.env
MONGODB_URI=mongodb://localhost:27017/event-management
JWT_SECRET=your-jwt-secret
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

3. Start the development servers:
```bash
npm run dev
```

## API Endpoints

### Settings API
- `GET /api/settings/profile` - Get user profile settings
- `PUT /api/settings/profile` - Update user profile
- `GET /api/settings/notifications` - Get notification preferences
- `PUT /api/settings/notifications` - Update notification preferences
- `GET /api/settings/events` - Get event settings
- `PUT /api/settings/events` - Update event settings
- `GET /api/settings/profit` - Get profit settings
- `PUT /api/settings/profit` - Update profit settings
- `GET /api/settings/security` - Get security settings
- `PUT /api/settings/security` - Update security settings
- `GET /api/settings/appearance` - Get appearance settings
- `PUT /api/settings/appearance` - Update appearance settings

## Technologies Used

- **Frontend**: React, React Router, Axios, Tailwind CSS
- **Backend**: Node.js, Express.js, MongoDB, Mongoose
- **Authentication**: JWT, bcrypt
- **File Upload**: Multer
- **Email**: Nodemailer
- **Development**: Concurrently, Nodemon

## License

MIT License
