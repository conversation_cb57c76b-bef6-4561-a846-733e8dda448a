{"ast": null, "code": "import $3aeG1$react from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nconst $ae1eeba8b9eafd08$export$5165eccb35aaadb5 = (0, $3aeG1$react).createContext({\n  register: () => {}\n});\n$ae1eeba8b9eafd08$export$5165eccb35aaadb5.displayName = 'PressResponderContext';\nexport { $ae1eeba8b9eafd08$export$5165eccb35aaadb5 as PressResponderContext };", "map": {"version": 3, "names": ["$ae1eeba8b9eafd08$export$5165eccb35aaadb5", "$3aeG1$react", "createContext", "register", "displayName"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\context.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {PressProps} from './usePress';\nimport React, {MutableRefObject} from 'react';\n\ninterface IPressResponderContext extends PressProps {\n  register(): void,\n  ref?: MutableRefObject<FocusableElement>\n}\n\nexport const PressResponderContext = React.createContext<IPressResponderContext>({register: () => {}});\nPressResponderContext.displayName = 'PressResponderContext';\n"], "mappings": ";;AAAA;;;;;;;;;;;AAqBO,MAAMA,yCAAA,GAAwB,IAAAC,YAAI,EAAEC,aAAa,CAAyB;EAACC,QAAA,EAAUA,CAAA,MAAO;AAAC;AACpGH,yCAAA,CAAsBI,WAAW,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}