{"ast": null, "code": "function _class_apply_descriptor_get(receiver, descriptor) {\n  if (descriptor.get) return descriptor.get.call(receiver);\n  return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };", "map": {"version": 3, "names": ["_class_apply_descriptor_get", "receiver", "descriptor", "get", "call", "value", "_"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "mappings": "AAAA,SAASA,2BAA2BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvD,IAAIA,UAAU,CAACC,GAAG,EAAE,OAAOD,UAAU,CAACC,GAAG,CAACC,IAAI,CAACH,QAAQ,CAAC;EAExD,OAAOC,UAAU,CAACG,KAAK;AAC3B;AACA,SAASL,2BAA2B,IAAIM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}