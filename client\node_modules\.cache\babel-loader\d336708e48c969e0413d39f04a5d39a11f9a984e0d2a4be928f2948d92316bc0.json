{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\setting\\\\client\\\\src\\\\components\\\\settings\\\\SecuritySettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { ShieldCheckIcon, DevicePhoneMobileIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst SecuritySettings = ({\n  settings,\n  onUpdate\n}) => {\n  _s();\n  var _formData$trustedDevi, _formData$loginHistor;\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    const newValue = type === 'number' ? parseInt(value) : value;\n    const newData = {\n      ...formData,\n      [name]: newValue\n    };\n    setFormData(newData);\n  };\n  const handleToggle = setting => {\n    const newData = {\n      ...formData,\n      [setting]: !formData[setting]\n    };\n    setFormData(newData);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n  const formatDate = date => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatDateTime = date => {\n    return new Date(date).toLocaleString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Security Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: \"Manage your account security and privacy settings.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), \"Authentication\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Two-Factor Authentication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Add an extra layer of security to your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.twoFactorAuth || false,\n              onChange: () => handleToggle('twoFactorAuth'),\n              disabled: !isEditing,\n              className: classNames(formData.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Two-factor authentication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.twoFactorAuth ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Login Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Get notified when someone logs into your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.loginNotifications || false,\n              onChange: () => handleToggle('loginNotifications'),\n              disabled: !isEditing,\n              className: classNames(formData.loginNotifications ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Login notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.loginNotifications ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sessionTimeout\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Session Timeout (minutes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"sessionTimeout\",\n              id: \"sessionTimeout\",\n              value: formData.sessionTimeout || 30,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              min: \"5\",\n              max: \"480\",\n              className: \"mt-1 block w-full sm:w-32 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Automatically log out after this period of inactivity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Last Changed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: formatDate(formData.passwordLastChanged)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Change Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(DevicePhoneMobileIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), \"Trusted Devices\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (_formData$trustedDevi = formData.trustedDevices) === null || _formData$trustedDevi === void 0 ? void 0 : _formData$trustedDevi.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between bg-white p-3 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DevicePhoneMobileIcon, {\n                className: \"h-5 w-5 text-gray-400 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: device.deviceName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [\"Last used: \", formatDateTime(device.lastUsed)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-red-600 hover:text-red-800 text-sm font-medium\",\n              children: \"Remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, device.deviceId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), \"Recent Login Activity\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (_formData$loginHistor = formData.loginHistory) === null || _formData$loginHistor === void 0 ? void 0 : _formData$loginHistor.slice(0, 5).map((login, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between bg-white p-3 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: classNames('w-2 h-2 rounded-full mr-3', login.success ? 'bg-green-400' : 'bg-red-400')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: login.success ? 'Successful login' : 'Failed login attempt'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [login.location, \" \\u2022 \", login.ip, \" \\u2022 \", formatDateTime(login.timestamp)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), !login.success && /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n              className: \"h-5 w-5 text-red-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n            children: \"View all activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n              className: \"h-5 w-5 text-yellow-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-yellow-800\",\n              children: \"Security Recommendation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-yellow-700\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [!formData.twoFactorAuth && \"Enable two-factor authentication for better security. \", \"Consider changing your password regularly and avoid using the same password across multiple sites.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setIsEditing(true),\n          className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Edit Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(SecuritySettings, \"rEZs2nirDlaVd6Cwh1oU/4Iupv4=\");\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "Switch", "ShieldCheckIcon", "DevicePhoneMobileIcon", "ClockIcon", "ExclamationTriangleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "SecuritySettings", "settings", "onUpdate", "_s", "_formData$trustedDevi", "_formData$loginHistor", "formData", "setFormData", "isEditing", "setIsEditing", "handleInputChange", "e", "name", "value", "type", "target", "newValue", "parseInt", "newData", "handleToggle", "setting", "handleSubmit", "preventDefault", "handleCancel", "formatDate", "date", "Date", "toLocaleDateString", "year", "month", "day", "formatDateTime", "toLocaleString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "checked", "twoFactorAuth", "onChange", "disabled", "loginNotifications", "htmlFor", "id", "sessionTimeout", "min", "max", "passwordLastChanged", "trustedDevices", "map", "device", "deviceName", "lastUsed", "deviceId", "loginHistory", "slice", "login", "index", "success", "location", "ip", "timestamp", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/src/components/settings/SecuritySettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { \n  ShieldCheckIcon, \n  DevicePhoneMobileIcon, \n  ClockIcon,\n  ExclamationTriangleIcon \n} from '@heroicons/react/24/outline';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst SecuritySettings = ({ settings, onUpdate }) => {\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    const newValue = type === 'number' ? parseInt(value) : value;\n    const newData = { ...formData, [name]: newValue };\n    setFormData(newData);\n  };\n\n  const handleToggle = (setting) => {\n    const newData = { ...formData, [setting]: !formData[setting] };\n    setFormData(newData);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n\n  const formatDate = (date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const formatDateTime = (date) => {\n    return new Date(date).toLocaleString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Security Settings</h3>\n        <p className=\"text-sm text-gray-600\">\n          Manage your account security and privacy settings.\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit}>\n        {/* Authentication Settings */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <ShieldCheckIcon className=\"h-5 w-5 mr-2\" />\n            Authentication\n          </h4>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Two-Factor Authentication</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Add an extra layer of security to your account\n                </p>\n              </div>\n              <Switch\n                checked={formData.twoFactorAuth || false}\n                onChange={() => handleToggle('twoFactorAuth')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Two-factor authentication</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.twoFactorAuth ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Login Notifications</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Get notified when someone logs into your account\n                </p>\n              </div>\n              <Switch\n                checked={formData.loginNotifications || false}\n                onChange={() => handleToggle('loginNotifications')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.loginNotifications ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Login notifications</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.loginNotifications ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div>\n              <label htmlFor=\"sessionTimeout\" className=\"block text-sm font-medium text-gray-700\">\n                Session Timeout (minutes)\n              </label>\n              <input\n                type=\"number\"\n                name=\"sessionTimeout\"\n                id=\"sessionTimeout\"\n                value={formData.sessionTimeout || 30}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                min=\"5\"\n                max=\"480\"\n                className=\"mt-1 block w-full sm:w-32 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Automatically log out after this period of inactivity\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Password Information */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Password</h4>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h5 className=\"text-sm font-medium text-gray-900\">Last Changed</h5>\n                <p className=\"text-sm text-gray-500\">\n                  {formatDate(formData.passwordLastChanged)}\n                </p>\n              </div>\n              <button\n                type=\"button\"\n                className=\"bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Change Password\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Trusted Devices */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <DevicePhoneMobileIcon className=\"h-5 w-5 mr-2\" />\n            Trusted Devices\n          </h4>\n          \n          <div className=\"space-y-3\">\n            {formData.trustedDevices?.map((device) => (\n              <div key={device.deviceId} className=\"flex items-center justify-between bg-white p-3 rounded-md\">\n                <div className=\"flex items-center\">\n                  <DevicePhoneMobileIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                  <div>\n                    <h5 className=\"text-sm font-medium text-gray-900\">{device.deviceName}</h5>\n                    <p className=\"text-sm text-gray-500\">\n                      Last used: {formatDateTime(device.lastUsed)}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  type=\"button\"\n                  className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n                >\n                  Remove\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Login History */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <ClockIcon className=\"h-5 w-5 mr-2\" />\n            Recent Login Activity\n          </h4>\n          \n          <div className=\"space-y-3\">\n            {formData.loginHistory?.slice(0, 5).map((login, index) => (\n              <div key={index} className=\"flex items-center justify-between bg-white p-3 rounded-md\">\n                <div className=\"flex items-center\">\n                  <div className={classNames(\n                    'w-2 h-2 rounded-full mr-3',\n                    login.success ? 'bg-green-400' : 'bg-red-400'\n                  )} />\n                  <div>\n                    <h5 className=\"text-sm font-medium text-gray-900\">\n                      {login.success ? 'Successful login' : 'Failed login attempt'}\n                    </h5>\n                    <p className=\"text-sm text-gray-500\">\n                      {login.location} • {login.ip} • {formatDateTime(login.timestamp)}\n                    </p>\n                  </div>\n                </div>\n                {!login.success && (\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500\" />\n                )}\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"mt-4\">\n            <button\n              type=\"button\"\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              View all activity\n            </button>\n          </div>\n        </div>\n\n        {/* Security Alert */}\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-yellow-800\">\n                Security Recommendation\n              </h3>\n              <div className=\"mt-2 text-sm text-yellow-700\">\n                <p>\n                  {!formData.twoFactorAuth && \"Enable two-factor authentication for better security. \"}\n                  Consider changing your password regularly and avoid using the same password across multiple sites.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3\">\n          {isEditing ? (\n            <>\n              <button\n                type=\"button\"\n                onClick={handleCancel}\n                className=\"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Save Changes\n              </button>\n            </>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={() => setIsEditing(true)}\n              className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Edit Settings\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SACEC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,uBAAuB,QAClB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAACgB,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMyB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGH,CAAC,CAACI,MAAM;IACtC,MAAMC,QAAQ,GAAGF,IAAI,KAAK,QAAQ,GAAGG,QAAQ,CAACJ,KAAK,CAAC,GAAGA,KAAK;IAC5D,MAAMK,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACM,IAAI,GAAGI;IAAS,CAAC;IACjDT,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMF,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACc,OAAO,GAAG,CAACd,QAAQ,CAACc,OAAO;IAAE,CAAC;IAC9Db,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMG,YAAY,GAAIV,CAAC,IAAK;IAC1BA,CAAC,CAACW,cAAc,CAAC,CAAC;IAClBpB,QAAQ,CAACI,QAAQ,CAAC;IAClBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzBhB,WAAW,CAACN,QAAQ,CAAC;IACrBQ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMe,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIN,IAAI,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACO,cAAc,CAAC,OAAO,EAAE;MAC5CJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdG,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAI2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EhD,OAAA;QAAG2C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhD,OAAA;MAAMiD,QAAQ,EAAEpB,YAAa;MAAAe,QAAA,gBAE3B5C,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACL,eAAe;YAACgD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5C,OAAA;YAAK2C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C5C,OAAA;cAAK2C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5C,OAAA;gBAAI2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFhD,OAAA;gBAAG2C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhD,OAAA,CAACN,MAAM;cACLwD,OAAO,EAAEpC,QAAQ,CAACqC,aAAa,IAAI,KAAM;cACzCC,QAAQ,EAAEA,CAAA,KAAMzB,YAAY,CAAC,eAAe,CAAE;cAC9C0B,QAAQ,EAAE,CAACrC,SAAU;cACrB2B,SAAS,EAAExC,UAAU,CACnBW,QAAQ,CAACqC,aAAa,GAAG,aAAa,GAAG,aAAa,EACtD,wNAAwN,EACxN,CAACnC,SAAS,IAAI,+BAChB,CAAE;cAAA4B,QAAA,gBAEF5C,OAAA;gBAAM2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DhD,OAAA;gBACE,eAAY,MAAM;gBAClB2C,SAAS,EAAExC,UAAU,CACnBW,QAAQ,CAACqC,aAAa,GAAG,eAAe,GAAG,eAAe,EAC1D,4HACF;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhD,OAAA;YAAK2C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C5C,OAAA;cAAK2C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5C,OAAA;gBAAI2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EhD,OAAA;gBAAG2C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhD,OAAA,CAACN,MAAM;cACLwD,OAAO,EAAEpC,QAAQ,CAACwC,kBAAkB,IAAI,KAAM;cAC9CF,QAAQ,EAAEA,CAAA,KAAMzB,YAAY,CAAC,oBAAoB,CAAE;cACnD0B,QAAQ,EAAE,CAACrC,SAAU;cACrB2B,SAAS,EAAExC,UAAU,CACnBW,QAAQ,CAACwC,kBAAkB,GAAG,aAAa,GAAG,aAAa,EAC3D,wNAAwN,EACxN,CAACtC,SAAS,IAAI,+BAChB,CAAE;cAAA4B,QAAA,gBAEF5C,OAAA;gBAAM2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDhD,OAAA;gBACE,eAAY,MAAM;gBAClB2C,SAAS,EAAExC,UAAU,CACnBW,QAAQ,CAACwC,kBAAkB,GAAG,eAAe,GAAG,eAAe,EAC/D,4HACF;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOuD,OAAO,EAAC,gBAAgB;cAACZ,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEpF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEsB,IAAI,EAAC,QAAQ;cACbF,IAAI,EAAC,gBAAgB;cACrBoC,EAAE,EAAC,gBAAgB;cACnBnC,KAAK,EAAEP,QAAQ,CAAC2C,cAAc,IAAI,EAAG;cACrCL,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrB0C,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACThB,SAAS,EAAC;YAAgK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K,CAAC,eACFhD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEtEhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB5C,OAAA;YAAK2C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAI2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEhD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjCZ,UAAU,CAAClB,QAAQ,CAAC8C,mBAAmB;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhD,OAAA;cACEsB,IAAI,EAAC,QAAQ;cACbqB,SAAS,EAAC,qMAAqM;cAAAC,QAAA,EAChN;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACJ,qBAAqB;YAAC+C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEpD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAAhC,qBAAA,GACvBE,QAAQ,CAAC+C,cAAc,cAAAjD,qBAAA,uBAAvBA,qBAAA,CAAyBkD,GAAG,CAAEC,MAAM,iBACnC/D,OAAA;YAA2B2C,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBAC9F5C,OAAA;cAAK2C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5C,OAAA,CAACJ,qBAAqB;gBAAC+C,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChEhD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAI2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEmB,MAAM,CAACC;gBAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1EhD,OAAA;kBAAG2C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,aACxB,EAACL,cAAc,CAACwB,MAAM,CAACE,QAAQ,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhD,OAAA;cACEsB,IAAI,EAAC,QAAQ;cACbqB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAChE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAfDe,MAAM,CAACG,QAAQ;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBpB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACH,SAAS;YAAC8C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAA/B,qBAAA,GACvBC,QAAQ,CAACqD,YAAY,cAAAtD,qBAAA,uBAArBA,qBAAA,CAAuBuD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,KAAK,EAAEC,KAAK,kBACnDtE,OAAA;YAAiB2C,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBACpF5C,OAAA;cAAK2C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5C,OAAA;gBAAK2C,SAAS,EAAExC,UAAU,CACxB,2BAA2B,EAC3BkE,KAAK,CAACE,OAAO,GAAG,cAAc,GAAG,YACnC;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACLhD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAI2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9CyB,KAAK,CAACE,OAAO,GAAG,kBAAkB,GAAG;gBAAsB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACLhD,OAAA;kBAAG2C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjCyB,KAAK,CAACG,QAAQ,EAAC,UAAG,EAACH,KAAK,CAACI,EAAE,EAAC,UAAG,EAAClC,cAAc,CAAC8B,KAAK,CAACK,SAAS,CAAC;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL,CAACqB,KAAK,CAACE,OAAO,iBACbvE,OAAA,CAACF,uBAAuB;cAAC6C,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC5D;UAAA,GAjBOsB,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhD,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5C,OAAA;YACEsB,IAAI,EAAC,QAAQ;YACbqB,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAClE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE5C,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5C,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5C,OAAA,CAACF,uBAAuB;cAAC6C,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5C,OAAA;cAAI2C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhD,OAAA;cAAK2C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C5C,OAAA;gBAAA4C,QAAA,GACG,CAAC9B,QAAQ,CAACqC,aAAa,IAAI,wDAAwD,EAAC,oGAEvF;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxC5B,SAAS,gBACRhB,OAAA,CAAAE,SAAA;UAAA0C,QAAA,gBACE5C,OAAA;YACEsB,IAAI,EAAC,QAAQ;YACbqD,OAAO,EAAE5C,YAAa;YACtBY,SAAS,EAAC,2LAA2L;YAAAC,QAAA,EACtM;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACEsB,IAAI,EAAC,QAAQ;YACbqB,SAAS,EAAC,+LAA+L;YAAAC,QAAA,EAC1M;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHhD,OAAA;UACEsB,IAAI,EAAC,QAAQ;UACbqD,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,IAAI,CAAE;UAClC0B,SAAS,EAAC,+LAA+L;UAAAC,QAAA,EAC1M;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CA7RIH,gBAAgB;AAAAoE,EAAA,GAAhBpE,gBAAgB;AA+RtB,eAAeA,gBAAgB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}