{"ast": null, "code": "import { env as t } from './env.js';\nfunction o(n) {\n  var e, r;\n  return t.isServer ? null : n ? \"ownerDocument\" in n ? n.ownerDocument : \"current\" in n ? (r = (e = n.current) == null ? void 0 : e.ownerDocument) != null ? r : document : null : document;\n}\nexport { o as getOwnerDocument };", "map": {"version": 3, "names": ["env", "t", "o", "n", "e", "r", "isServer", "ownerDocument", "current", "document", "getOwnerDocument"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/utils/owner.js"], "sourcesContent": ["import{env as t}from'./env.js';function o(n){var e,r;return t.isServer?null:n?\"ownerDocument\"in n?n.ownerDocument:\"current\"in n?(r=(e=n.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}export{o as getOwnerDocument};\n"], "mappings": "AAAA,SAAOA,GAAG,IAAIC,CAAC,QAAK,UAAU;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,EAACC,CAAC;EAAC,OAAOJ,CAAC,CAACK,QAAQ,GAAC,IAAI,GAACH,CAAC,GAAC,eAAe,IAAGA,CAAC,GAACA,CAAC,CAACI,aAAa,GAAC,SAAS,IAAGJ,CAAC,GAAC,CAACE,CAAC,GAAC,CAACD,CAAC,GAACD,CAAC,CAACK,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACJ,CAAC,CAACG,aAAa,KAAG,IAAI,GAACF,CAAC,GAACI,QAAQ,GAAC,IAAI,GAACA,QAAQ;AAAA;AAAC,SAAOP,CAAC,IAAIQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}