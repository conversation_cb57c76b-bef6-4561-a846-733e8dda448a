{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $93925083ecbb358c$export$48d1ea**********(handler) {\n  if (!handler) return undefined;\n  let shouldStopPropagation = true;\n  return e => {\n    let event = {\n      ...e,\n      preventDefault() {\n        e.preventDefault();\n      },\n      isDefaultPrevented() {\n        return e.isDefaultPrevented();\n      },\n      stopPropagation() {\n        if (shouldStopPropagation && process.env.NODE_ENV !== 'production') console.error('stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.');else shouldStopPropagation = true;\n      },\n      continuePropagation() {\n        shouldStopPropagation = false;\n      },\n      isPropagationStopped() {\n        return shouldStopPropagation;\n      }\n    };\n    handler(event);\n    if (shouldStopPropagation) e.stopPropagation();\n  };\n}\nexport { $93925083ecbb358c$export$48d1ea********** as createEventHandler };", "map": {"version": 3, "names": ["$93925083ecbb358c$export$48d1ea**********", "handler", "undefined", "shouldStopPropagation", "e", "event", "preventDefault", "isDefaultPrevented", "stopPropagation", "process", "env", "NODE_ENV", "console", "error", "continuePropagation", "isPropagationStopped"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\createEventHandler.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BaseEvent} from '@react-types/shared';\nimport {SyntheticEvent} from 'react';\n\n/**\n * This function wraps a React event handler to make stopPropagation the default, and support continuePropagation instead.\n */\nexport function createEventHandler<T extends SyntheticEvent>(handler?: (e: BaseEvent<T>) => void): ((e: T) => void) | undefined {\n  if (!handler) {\n    return undefined;\n  }\n\n  let shouldStopPropagation = true;\n  return (e: T) => {\n    let event: BaseEvent<T> = {\n      ...e,\n      preventDefault() {\n        e.preventDefault();\n      },\n      isDefaultPrevented() {\n        return e.isDefaultPrevented();\n      },\n      stopPropagation() {\n        if (shouldStopPropagation && process.env.NODE_ENV !== 'production') {\n          console.error('stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.');\n        } else {\n          shouldStopPropagation = true;\n        }\n      },\n      continuePropagation() {\n        shouldStopPropagation = false;\n      },\n      isPropagationStopped() {\n        return shouldStopPropagation;\n      }\n    };\n\n    handler(event);\n\n    if (shouldStopPropagation) {\n      e.stopPropagation();\n    }\n  };\n}\n"], "mappings": "AAAA;;;;;;;;;;GAkBO,SAASA,0CAA6CC,OAAmC;EAC9F,IAAI,CAACA,OAAA,EACH,OAAOC,SAAA;EAGT,IAAIC,qBAAA,GAAwB;EAC5B,OAAQC,CAAA;IACN,IAAIC,KAAA,GAAsB;MACxB,GAAGD,CAAC;MACJE,eAAA;QACEF,CAAA,CAAEE,cAAc;MAClB;MACAC,mBAAA;QACE,OAAOH,CAAA,CAAEG,kBAAkB;MAC7B;MACAC,gBAAA;QACE,IAAIL,qBAAA,IAAyBM,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cACpDC,OAAA,CAAQC,KAAK,CAAC,6IAEdV,qBAAA,GAAwB;MAE5B;MACAW,oBAAA;QACEX,qBAAA,GAAwB;MAC1B;MACAY,qBAAA;QACE,OAAOZ,qBAAA;MACT;IACF;IAEAF,OAAA,CAAQI,KAAA;IAER,IAAIF,qBAAA,EACFC,CAAA,CAAEI,eAAe;EAErB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}