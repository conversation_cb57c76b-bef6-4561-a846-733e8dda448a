{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet $bbed8b41f857bcc0$var$transitionsByElement = new Map();\n// A list of callbacks to call once there are no transitioning elements.\nlet $bbed8b41f857bcc0$var$transitionCallbacks = new Set();\nfunction $bbed8b41f857bcc0$var$setupGlobalEvents() {\n  if (typeof window === 'undefined') return;\n  function isTransitionEvent(event) {\n    return 'propertyName' in event;\n  }\n  let onTransitionStart = e => {\n    if (!isTransitionEvent(e) || !e.target) return;\n    // Add the transitioning property to the list for this element.\n    let transitions = $bbed8b41f857bcc0$var$transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      $bbed8b41f857bcc0$var$transitionsByElement.set(e.target, transitions);\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n    transitions.add(e.propertyName);\n  };\n  let onTransitionEnd = e => {\n    if (!isTransitionEvent(e) || !e.target) return;\n    // Remove property from list of transitioning properties.\n    let properties = $bbed8b41f857bcc0$var$transitionsByElement.get(e.target);\n    if (!properties) return;\n    properties.delete(e.propertyName);\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      $bbed8b41f857bcc0$var$transitionsByElement.delete(e.target);\n    }\n    // If no transitioning elements, call all of the queued callbacks.\n    if ($bbed8b41f857bcc0$var$transitionsByElement.size === 0) {\n      for (let cb of $bbed8b41f857bcc0$var$transitionCallbacks) cb();\n      $bbed8b41f857bcc0$var$transitionCallbacks.clear();\n    }\n  };\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') $bbed8b41f857bcc0$var$setupGlobalEvents();else document.addEventListener('DOMContentLoaded', $bbed8b41f857bcc0$var$setupGlobalEvents);\n}\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction $bbed8b41f857bcc0$var$cleanupDetachedElements() {\n  for (const [eventTarget] of $bbed8b41f857bcc0$var$transitionsByElement)\n  // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n  // the explicit instanceof check, since it may be different in different contexts.\n  if ('isConnected' in eventTarget && !eventTarget.isConnected) $bbed8b41f857bcc0$var$transitionsByElement.delete(eventTarget);\n}\nfunction $bbed8b41f857bcc0$export$24490316f764c430(fn) {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    $bbed8b41f857bcc0$var$cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if ($bbed8b41f857bcc0$var$transitionsByElement.size === 0) fn();else $bbed8b41f857bcc0$var$transitionCallbacks.add(fn);\n  });\n}\nexport { $bbed8b41f857bcc0$export$24490316f764c430 as runAfterTransition };", "map": {"version": 3, "names": ["$bbed8b41f857bcc0$var$transitionsByElement", "Map", "$bbed8b41f857bcc0$var$transitionCallbacks", "Set", "$bbed8b41f857bcc0$var$setupGlobalEvents", "window", "isTransitionEvent", "event", "onTransitionStart", "e", "target", "transitions", "get", "set", "addEventListener", "onTransitionEnd", "once", "add", "propertyName", "properties", "delete", "size", "removeEventListener", "cb", "clear", "document", "body", "readyState", "$bbed8b41f857bcc0$var$cleanupDetachedElements", "eventTarget", "isConnected", "$bbed8b41f857bcc0$export$24490316f764c430", "fn", "requestAnimationFrame"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\runAfterTransition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n"], "mappings": "AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,0CAAA,GAAuB,IAAIC,GAAA;AAE/B;AACA,IAAIC,yCAAA,GAAsB,IAAIC,GAAA;AAE9B,SAASC,wCAAA;EACP,IAAI,OAAOC,MAAA,KAAW,aACpB;EAGF,SAASC,kBAAkBC,KAAY;IACrC,OAAO,kBAAkBA,KAAA;EAC3B;EAEA,IAAIC,iBAAA,GAAqBC,CAAA;IACvB,IAAI,CAACH,iBAAA,CAAkBG,CAAA,KAAM,CAACA,CAAA,CAAEC,MAAM,EACpC;IAEF;IACA,IAAIC,WAAA,GAAcX,0CAAA,CAAqBY,GAAG,CAACH,CAAA,CAAEC,MAAM;IACnD,IAAI,CAACC,WAAA,EAAa;MAChBA,WAAA,GAAc,IAAIR,GAAA;MAClBH,0CAAA,CAAqBa,GAAG,CAACJ,CAAA,CAAEC,MAAM,EAAEC,WAAA;MAEnC;MACA;MACA;MACAF,CAAA,CAAEC,MAAM,CAACI,gBAAgB,CAAC,oBAAoBC,eAAA,EAAiB;QAC7DC,IAAA,EAAM;MACR;IACF;IAEAL,WAAA,CAAYM,GAAG,CAACR,CAAA,CAAES,YAAY;EAChC;EAEA,IAAIH,eAAA,GAAmBN,CAAA;IACrB,IAAI,CAACH,iBAAA,CAAkBG,CAAA,KAAM,CAACA,CAAA,CAAEC,MAAM,EACpC;IAEF;IACA,IAAIS,UAAA,GAAanB,0CAAA,CAAqBY,GAAG,CAACH,CAAA,CAAEC,MAAM;IAClD,IAAI,CAACS,UAAA,EACH;IAGFA,UAAA,CAAWC,MAAM,CAACX,CAAA,CAAES,YAAY;IAEhC;IACA,IAAIC,UAAA,CAAWE,IAAI,KAAK,GAAG;MACzBZ,CAAA,CAAEC,MAAM,CAACY,mBAAmB,CAAC,oBAAoBP,eAAA;MACjDf,0CAAA,CAAqBoB,MAAM,CAACX,CAAA,CAAEC,MAAM;IACtC;IAEA;IACA,IAAIV,0CAAA,CAAqBqB,IAAI,KAAK,GAAG;MACnC,KAAK,IAAIE,EAAA,IAAMrB,yCAAA,EACbqB,EAAA;MAGFrB,yCAAA,CAAoBsB,KAAK;IAC3B;EACF;EAEAC,QAAA,CAASC,IAAI,CAACZ,gBAAgB,CAAC,iBAAiBN,iBAAA;EAChDiB,QAAA,CAASC,IAAI,CAACZ,gBAAgB,CAAC,iBAAiBC,eAAA;AAClD;AAEA,IAAI,OAAOU,QAAA,KAAa;EACtB,IAAIA,QAAA,CAASE,UAAU,KAAK,WAC1BvB,uCAAA,QAEAqB,QAAA,CAASX,gBAAgB,CAAC,oBAAoBV,uCAAA;;AAIlD;;;;;AAKA,SAASwB,8CAAA;EACP,KAAK,MAAM,CAACC,WAAA,CAAY,IAAI7B,0CAAA;EAC1B;EACA;EACA,IAAI,iBAAiB6B,WAAA,IAAe,CAACA,WAAA,CAAYC,WAAW,EAC1D9B,0CAAA,CAAqBoB,MAAM,CAACS,WAAA;AAGlC;AAEO,SAASE,0CAAmBC,EAAc;EAC/C;EACAC,qBAAA,CAAsB;IACpBL,6CAAA;IACA;IACA;IACA,IAAI5B,0CAAA,CAAqBqB,IAAI,KAAK,GAChCW,EAAA,QAEA9B,yCAAA,CAAoBe,GAAG,CAACe,EAAA;EAE5B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}