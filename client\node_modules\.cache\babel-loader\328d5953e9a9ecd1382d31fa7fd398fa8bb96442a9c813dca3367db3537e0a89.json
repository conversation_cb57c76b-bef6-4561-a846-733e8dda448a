{"ast": null, "code": "\"use client\";\n\nimport r, { use<PERSON>emo as f } from \"react\";\nimport { useId as P } from '../../hooks/use-id.js';\nimport { Disabled<PERSON>rovider as y, useDisabled as u } from '../../internal/disabled.js';\nimport { FormFieldsProvider as D } from '../../internal/form-fields.js';\nimport { IdProvider as v } from '../../internal/id.js';\nimport { forwardRefWithAs as b, useRender as E } from '../../utils/render.js';\nimport { useDescriptions as A } from '../description/description.js';\nimport { useLabels as L } from '../label/label.js';\nlet _ = \"div\";\nfunction c(d, l) {\n  let t = `headlessui-control-${P()}`,\n    [s, p] = L(),\n    [n, a] = A(),\n    m = u(),\n    {\n      disabled: e = m || !1,\n      ...i\n    } = d,\n    o = f(() => ({\n      disabled: e\n    }), [e]),\n    F = {\n      ref: l,\n      disabled: e || void 0,\n      \"aria-disabled\": e || void 0\n    },\n    T = E();\n  return r.createElement(y, {\n    value: e\n  }, r.createElement(p, {\n    value: s\n  }, r.createElement(a, {\n    value: n\n  }, r.createElement(v, {\n    id: t\n  }, T({\n    ourProps: F,\n    theirProps: {\n      ...i,\n      children: r.createElement(D, null, typeof i.children == \"function\" ? i.children(o) : i.children)\n    },\n    slot: o,\n    defaultTag: _,\n    name: \"Field\"\n  })))));\n}\nlet H = b(c);\nexport { H as Field };", "map": {"version": 3, "names": ["r", "useMemo", "f", "useId", "P", "Disable<PERSON><PERSON><PERSON><PERSON>", "y", "useDisabled", "u", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "D", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "forwardRefWithAs", "b", "useRender", "E", "useDescriptions", "A", "useLabels", "L", "_", "c", "d", "l", "t", "s", "p", "n", "a", "m", "disabled", "e", "i", "o", "F", "ref", "T", "createElement", "value", "id", "ourProps", "theirProps", "children", "slot", "defaultTag", "name", "H", "Field"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/components/field/field.js"], "sourcesContent": ["\"use client\";import r,{use<PERSON>emo as f}from\"react\";import{useId as P}from'../../hooks/use-id.js';import{Disabled<PERSON>rovider as y,useDisabled as u}from'../../internal/disabled.js';import{FormFieldsProvider as D}from'../../internal/form-fields.js';import{IdProvider as v}from'../../internal/id.js';import{forwardRefWithAs as b,useRender as E}from'../../utils/render.js';import{useDescriptions as A}from'../description/description.js';import{useLabels as L}from'../label/label.js';let _=\"div\";function c(d,l){let t=`headlessui-control-${P()}`,[s,p]=L(),[n,a]=A(),m=u(),{disabled:e=m||!1,...i}=d,o=f(()=>({disabled:e}),[e]),F={ref:l,disabled:e||void 0,\"aria-disabled\":e||void 0},T=E();return r.createElement(y,{value:e},r.createElement(p,{value:s},r.createElement(a,{value:n},r.createElement(v,{id:t},T({ourProps:F,theirProps:{...i,children:r.createElement(D,null,typeof i.children==\"function\"?i.children(o):i.children)},slot:o,defaultTag:_,name:\"Field\"})))))}let H=b(c);export{H as Field};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,IAAEC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAAC,KAAK;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,sBAAsBrB,CAAC,CAAC,CAAC,EAAE;IAAC,CAACsB,CAAC,EAACC,CAAC,CAAC,GAACP,CAAC,CAAC,CAAC;IAAC,CAACQ,CAAC,EAACC,CAAC,CAAC,GAACX,CAAC,CAAC,CAAC;IAACY,CAAC,GAACtB,CAAC,CAAC,CAAC;IAAC;MAACuB,QAAQ,EAACC,CAAC,GAACF,CAAC,IAAE,CAAC,CAAC;MAAC,GAAGG;IAAC,CAAC,GAACV,CAAC;IAACW,CAAC,GAAChC,CAAC,CAAC,OAAK;MAAC6B,QAAQ,EAACC;IAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;IAACG,CAAC,GAAC;MAACC,GAAG,EAACZ,CAAC;MAACO,QAAQ,EAACC,CAAC,IAAE,KAAK,CAAC;MAAC,eAAe,EAACA,CAAC,IAAE,KAAK;IAAC,CAAC;IAACK,CAAC,GAACrB,CAAC,CAAC,CAAC;EAAC,OAAOhB,CAAC,CAACsC,aAAa,CAAChC,CAAC,EAAC;IAACiC,KAAK,EAACP;EAAC,CAAC,EAAChC,CAAC,CAACsC,aAAa,CAACX,CAAC,EAAC;IAACY,KAAK,EAACb;EAAC,CAAC,EAAC1B,CAAC,CAACsC,aAAa,CAACT,CAAC,EAAC;IAACU,KAAK,EAACX;EAAC,CAAC,EAAC5B,CAAC,CAACsC,aAAa,CAAC1B,CAAC,EAAC;IAAC4B,EAAE,EAACf;EAAC,CAAC,EAACY,CAAC,CAAC;IAACI,QAAQ,EAACN,CAAC;IAACO,UAAU,EAAC;MAAC,GAAGT,CAAC;MAACU,QAAQ,EAAC3C,CAAC,CAACsC,aAAa,CAAC5B,CAAC,EAAC,IAAI,EAAC,OAAOuB,CAAC,CAACU,QAAQ,IAAE,UAAU,GAACV,CAAC,CAACU,QAAQ,CAACT,CAAC,CAAC,GAACD,CAAC,CAACU,QAAQ;IAAC,CAAC;IAACC,IAAI,EAACV,CAAC;IAACW,UAAU,EAACxB,CAAC;IAACyB,IAAI,EAAC;EAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAACjC,CAAC,CAACQ,CAAC,CAAC;AAAC,SAAOyB,CAAC,IAAIC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}