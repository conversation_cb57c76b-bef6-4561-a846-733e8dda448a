const Notification = require('../models/Notification');

// GET user notifications
const getUserNotifications = async (req, res) => {
  try {
    const { userId } = req.params;
    const notifications = await Notification.find({ userId })
      .sort({ createdAt: -1 });
    
    res.json({ 
      success: true, 
      data: notifications 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// PUT mark notification as read
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    
    const notification = await Notification.findByIdAndUpdate(
      id, 
      { read: true }, 
      { new: true }
    );
    
    if (!notification) {
      return res.status(404).json({ 
        success: false, 
        message: 'Notification not found' 
      });
    }
    
    res.json({ 
      success: true, 
      message: 'Notification marked as read',
      data: notification 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// POST create notification
const createNotification = async (req, res) => {
  try {
    const { userId, message } = req.body;
    
    const notification = new Notification({
      userId,
      message
    });
    
    await notification.save();
    
    res.status(201).json({ 
      success: true, 
      message: 'Notification created successfully',
      data: notification 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

module.exports = {
  getUserNotifications,
  markAsRead,
  createNotification
};
