{"ast": null, "code": "import { useRef as u } from \"react\";\nimport * as r from '../utils/dom.js';\nimport { useEvent as o } from './use-event.js';\nimport { useEventListener as s } from './use-event-listener.js';\nfunction f(e) {\n  let l = u({\n    value: \"\",\n    selectionStart: null,\n    selectionEnd: null\n  });\n  return s(e, \"blur\", n => {\n    let t = n.target;\n    r.isHTMLInputElement(t) && (l.current = {\n      value: t.value,\n      selectionStart: t.selectionStart,\n      selectionEnd: t.selectionEnd\n    });\n  }), o(() => {\n    if (document.activeElement !== e && r.isHTMLInputElement(e) && e.isConnected) {\n      if (e.focus({\n        preventScroll: !0\n      }), e.value !== l.current.value) e.setSelectionRange(e.value.length, e.value.length);else {\n        let {\n          selectionStart: n,\n          selectionEnd: t\n        } = l.current;\n        n !== null && t !== null && e.setSelectionRange(n, t);\n      }\n      l.current = {\n        value: \"\",\n        selectionStart: null,\n        selectionEnd: null\n      };\n    }\n  });\n}\nexport { f as useRefocusableInput };", "map": {"version": 3, "names": ["useRef", "u", "r", "useEvent", "o", "useEventListener", "s", "f", "e", "l", "value", "selectionStart", "selectionEnd", "n", "t", "target", "isHTMLInputElement", "current", "document", "activeElement", "isConnected", "focus", "preventScroll", "setSelectionRange", "length", "useRefocusableInput"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-refocusable-input.js"], "sourcesContent": ["import{useRef as u}from\"react\";import*as r from'../utils/dom.js';import{useEvent as o}from'./use-event.js';import{useEventListener as s}from'./use-event-listener.js';function f(e){let l=u({value:\"\",selectionStart:null,selectionEnd:null});return s(e,\"blur\",n=>{let t=n.target;r.isHTMLInputElement(t)&&(l.current={value:t.value,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd})}),o(()=>{if(document.activeElement!==e&&r.isHTMLInputElement(e)&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==l.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:n,selectionEnd:t}=l.current;n!==null&&t!==null&&e.setSelectionRange(n,t)}l.current={value:\"\",selectionStart:null,selectionEnd:null}}})}export{f as useRefocusableInput};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAAC;IAACS,KAAK,EAAC,EAAE;IAACC,cAAc,EAAC,IAAI;IAACC,YAAY,EAAC;EAAI,CAAC,CAAC;EAAC,OAAON,CAAC,CAACE,CAAC,EAAC,MAAM,EAACK,CAAC,IAAE;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,MAAM;IAACb,CAAC,CAACc,kBAAkB,CAACF,CAAC,CAAC,KAAGL,CAAC,CAACQ,OAAO,GAAC;MAACP,KAAK,EAACI,CAAC,CAACJ,KAA<PERSON>;MAACC,cAAc,EAACG,CAAC,CAACH,cAAc;MAACC,YAAY,EAACE,CAAC,CAACF;IAAY,CAAC,CAAC;EAAA,CAAC,CAAC,EAACR,CAAC,CAAC,MAAI;IAAC,IAAGc,QAAQ,CAACC,aAAa,KAAGX,CAAC,IAAEN,CAAC,CAACc,kBAAkB,CAACR,CAAC,CAAC,IAAEA,CAAC,CAACY,WAAW,EAAC;MAAC,IAAGZ,CAAC,CAACa,KAAK,CAAC;QAACC,aAAa,EAAC,CAAC;MAAC,CAAC,CAAC,EAACd,CAAC,CAACE,KAAK,KAAGD,CAAC,CAACQ,OAAO,CAACP,KAAK,EAACF,CAAC,CAACe,iBAAiB,CAACf,CAAC,CAACE,KAAK,CAACc,MAAM,EAAChB,CAAC,CAACE,KAAK,CAACc,MAAM,CAAC,CAAC,KAAI;QAAC,IAAG;UAACb,cAAc,EAACE,CAAC;UAACD,YAAY,EAACE;QAAC,CAAC,GAACL,CAAC,CAACQ,OAAO;QAACJ,CAAC,KAAG,IAAI,IAAEC,CAAC,KAAG,IAAI,IAAEN,CAAC,CAACe,iBAAiB,CAACV,CAAC,EAACC,CAAC,CAAC;MAAA;MAACL,CAAC,CAACQ,OAAO,GAAC;QAACP,KAAK,EAAC,EAAE;QAACC,cAAc,EAAC,IAAI;QAACC,YAAY,EAAC;MAAI,CAAC;IAAA;EAAC,CAAC,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIkB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}