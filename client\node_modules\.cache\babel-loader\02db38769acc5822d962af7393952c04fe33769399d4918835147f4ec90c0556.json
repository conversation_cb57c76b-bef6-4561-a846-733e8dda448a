{"ast": null, "code": "import { useEffect as l } from \"react\";\nimport { disposables as u } from '../utils/disposables.js';\nimport * as c from '../utils/dom.js';\nimport { useLatestValue as d } from './use-latest-value.js';\nfunction p(s, n, o) {\n  let i = d(t => {\n    let e = t.getBoundingClientRect();\n    e.x === 0 && e.y === 0 && e.width === 0 && e.height === 0 && o();\n  });\n  l(() => {\n    if (!s) return;\n    let t = n === null ? null : c.isHTMLElement(n) ? n : n.current;\n    if (!t) return;\n    let e = u();\n    if (typeof ResizeObserver != \"undefined\") {\n      let r = new ResizeObserver(() => i.current(t));\n      r.observe(t), e.add(() => r.disconnect());\n    }\n    if (typeof IntersectionObserver != \"undefined\") {\n      let r = new IntersectionObserver(() => i.current(t));\n      r.observe(t), e.add(() => r.disconnect());\n    }\n    return () => e.dispose();\n  }, [n, i, s]);\n}\nexport { p as useOnDisappear };", "map": {"version": 3, "names": ["useEffect", "l", "disposables", "u", "c", "useLatestValue", "d", "p", "s", "n", "o", "i", "t", "e", "getBoundingClientRect", "x", "y", "width", "height", "isHTMLElement", "current", "ResizeObserver", "r", "observe", "add", "disconnect", "IntersectionObserver", "dispose", "useOnDisappear"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-on-disappear.js"], "sourcesContent": ["import{useEffect as l}from\"react\";import{disposables as u}from'../utils/disposables.js';import*as c from'../utils/dom.js';import{useLatestValue as d}from'./use-latest-value.js';function p(s,n,o){let i=d(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&o()});l(()=>{if(!s)return;let t=n===null?null:c.isHTMLElement(n)?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!=\"undefined\"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!=\"undefined\"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{p as useOnDisappear};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACM,CAAC,IAAE;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,qBAAqB,CAAC,CAAC;IAACD,CAAC,CAACE,CAAC,KAAG,CAAC,IAAEF,CAAC,CAACG,CAAC,KAAG,CAAC,IAAEH,CAAC,CAACI,KAAK,KAAG,CAAC,IAAEJ,CAAC,CAACK,MAAM,KAAG,CAAC,IAAER,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC;EAACT,CAAC,CAAC,MAAI;IAAC,IAAG,CAACO,CAAC,EAAC;IAAO,IAAII,CAAC,GAACH,CAAC,KAAG,IAAI,GAAC,IAAI,GAACL,CAAC,CAACe,aAAa,CAACV,CAAC,CAAC,GAACA,CAAC,GAACA,CAAC,CAACW,OAAO;IAAC,IAAG,CAACR,CAAC,EAAC;IAAO,IAAIC,CAAC,GAACV,CAAC,CAAC,CAAC;IAAC,IAAG,OAAOkB,cAAc,IAAE,WAAW,EAAC;MAAC,IAAIC,CAAC,GAAC,IAAID,cAAc,CAAC,MAAIV,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,CAAC;MAACU,CAAC,CAACC,OAAO,CAACX,CAAC,CAAC,EAACC,CAAC,CAACW,GAAG,CAAC,MAAIF,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC;IAAA;IAAC,IAAG,OAAOC,oBAAoB,IAAE,WAAW,EAAC;MAAC,IAAIJ,CAAC,GAAC,IAAII,oBAAoB,CAAC,MAAIf,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,CAAC;MAACU,CAAC,CAACC,OAAO,CAACX,CAAC,CAAC,EAACC,CAAC,CAACW,GAAG,CAAC,MAAIF,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC;IAAA;IAAC,OAAM,MAAIZ,CAAC,CAACc,OAAO,CAAC,CAAC;EAAA,CAAC,EAAC,CAAClB,CAAC,EAACE,CAAC,EAACH,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIqB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}