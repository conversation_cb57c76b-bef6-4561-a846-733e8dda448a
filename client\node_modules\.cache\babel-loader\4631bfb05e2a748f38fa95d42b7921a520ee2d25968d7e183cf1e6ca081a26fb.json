{"ast": null, "code": "import { useRef as a, useState as m } from \"react\";\nimport { getOwnerDocument as d } from '../utils/owner.js';\nimport { useDisposables as g } from './use-disposables.js';\nimport { useEvent as u } from './use-event.js';\nfunction E(e) {\n  let t = e.width / 2,\n    n = e.height / 2;\n  return {\n    top: e.clientY - n,\n    right: e.clientX + t,\n    bottom: e.clientY + n,\n    left: e.clientX - t\n  };\n}\nfunction P(e, t) {\n  return !(!e || !t || e.right < t.left || e.left > t.right || e.bottom < t.top || e.top > t.bottom);\n}\nfunction w({\n  disabled: e = !1\n} = {}) {\n  let t = a(null),\n    [n, l] = m(!1),\n    r = g(),\n    o = u(() => {\n      t.current = null, l(!1), r.dispose();\n    }),\n    f = u(s => {\n      if (r.dispose(), t.current === null) {\n        t.current = s.currentTarget, l(!0);\n        {\n          let i = d(s.currentTarget);\n          r.addEventListener(i, \"pointerup\", o, !1), r.addEventListener(i, \"pointermove\", c => {\n            if (t.current) {\n              let p = E(c);\n              l(P(p, t.current.getBoundingClientRect()));\n            }\n          }, !1), r.addEventListener(i, \"pointercancel\", o, !1);\n        }\n      }\n    });\n  return {\n    pressed: n,\n    pressProps: e ? {} : {\n      onPointerDown: f,\n      onPointerUp: o,\n      onClick: o\n    }\n  };\n}\nexport { w as useActivePress };", "map": {"version": 3, "names": ["useRef", "a", "useState", "m", "getOwnerDocument", "d", "useDisposables", "g", "useEvent", "u", "E", "e", "t", "width", "n", "height", "top", "clientY", "right", "clientX", "bottom", "left", "P", "w", "disabled", "l", "r", "o", "current", "dispose", "f", "s", "currentTarget", "i", "addEventListener", "c", "p", "getBoundingClientRect", "pressed", "pressProps", "onPointerDown", "onPointerUp", "onClick", "useActivePress"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-active-press.js"], "sourcesContent": ["import{useRef as a,useState as m}from\"react\";import{getOwnerDocument as d}from'../utils/owner.js';import{useDisposables as g}from'./use-disposables.js';import{useEvent as u}from'./use-event.js';function E(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function P(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function w({disabled:e=!1}={}){let t=a(null),[n,l]=m(!1),r=g(),o=u(()=>{t.current=null,l(!1),r.dispose()}),f=u(s=>{if(r.dispose(),t.current===null){t.current=s.currentTarget,l(!0);{let i=d(s.currentTarget);r.addEventListener(i,\"pointerup\",o,!1),r.addEventListener(i,\"pointermove\",c=>{if(t.current){let p=E(c);l(P(p,t.current.getBoundingClientRect()))}},!1),r.addEventListener(i,\"pointercancel\",o,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:f,onPointerUp:o,onClick:o}}}export{w as useActivePress};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,KAAK,GAAC,CAAC;IAACC,CAAC,GAACH,CAAC,CAACI,MAAM,GAAC,CAAC;EAAC,OAAM;IAACC,GAAG,EAACL,CAAC,CAACM,OAAO,GAACH,CAAC;IAACI,KAAK,EAACP,CAAC,CAACQ,OAAO,GAACP,CAAC;IAACQ,MAAM,EAACT,CAAC,CAACM,OAAO,GAACH,CAAC;IAACO,IAAI,EAACV,CAAC,CAACQ,OAAO,GAACP;EAAC,CAAC;AAAA;AAAC,SAASU,CAACA,CAACX,CAAC,EAACC,CAAC,EAAC;EAAC,OAAM,EAAE,CAACD,CAAC,IAAE,CAACC,CAAC,IAAED,CAAC,CAACO,KAAK,GAACN,CAAC,CAACS,IAAI,IAAEV,CAAC,CAACU,IAAI,GAACT,CAAC,CAACM,KAAK,IAAEP,CAAC,CAACS,MAAM,GAACR,CAAC,CAACI,GAAG,IAAEL,CAAC,CAACK,GAAG,GAACJ,CAAC,CAACQ,MAAM,CAAC;AAAA;AAAC,SAASG,CAACA,CAAC;EAACC,QAAQ,EAACb,CAAC,GAAC,CAAC;AAAC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACX,CAAC,CAAC,IAAI,CAAC;IAAC,CAACa,CAAC,EAACW,CAAC,CAAC,GAACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IAACuB,CAAC,GAACnB,CAAC,CAAC,CAAC;IAACoB,CAAC,GAAClB,CAAC,CAAC,MAAI;MAACG,CAAC,CAACgB,OAAO,GAAC,IAAI,EAACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAACC,CAAC,CAACG,OAAO,CAAC,CAAC;IAAA,CAAC,CAAC;IAACC,CAAC,GAACrB,CAAC,CAACsB,CAAC,IAAE;MAAC,IAAGL,CAAC,CAACG,OAAO,CAAC,CAAC,EAACjB,CAAC,CAACgB,OAAO,KAAG,IAAI,EAAC;QAAChB,CAAC,CAACgB,OAAO,GAACG,CAAC,CAACC,aAAa,EAACP,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC;UAAC,IAAIQ,CAAC,GAAC5B,CAAC,CAAC0B,CAAC,CAACC,aAAa,CAAC;UAACN,CAAC,CAACQ,gBAAgB,CAACD,CAAC,EAAC,WAAW,EAACN,CAAC,EAAC,CAAC,CAAC,CAAC,EAACD,CAAC,CAACQ,gBAAgB,CAACD,CAAC,EAAC,aAAa,EAACE,CAAC,IAAE;YAAC,IAAGvB,CAAC,CAACgB,OAAO,EAAC;cAAC,IAAIQ,CAAC,GAAC1B,CAAC,CAACyB,CAAC,CAAC;cAACV,CAAC,CAACH,CAAC,CAACc,CAAC,EAACxB,CAAC,CAACgB,OAAO,CAACS,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAAA;UAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAACX,CAAC,CAACQ,gBAAgB,CAACD,CAAC,EAAC,eAAe,EAACN,CAAC,EAAC,CAAC,CAAC,CAAC;QAAA;MAAC;IAAC,CAAC,CAAC;EAAC,OAAM;IAACW,OAAO,EAACxB,CAAC;IAACyB,UAAU,EAAC5B,CAAC,GAAC,CAAC,CAAC,GAAC;MAAC6B,aAAa,EAACV,CAAC;MAACW,WAAW,EAACd,CAAC;MAACe,OAAO,EAACf;IAAC;EAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIoB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}