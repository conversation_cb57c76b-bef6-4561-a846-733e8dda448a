{"ast": null, "code": "import { useId as $bdb11010cef70236$export$f680877a34711e37 } from \"./useId.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $313b98861ee5dd6c$export$d6875122194c7b44(props, defaultLabel) {\n  let {\n    id: id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = (0, $bdb11010cef70236$export$f680877a34711e37)(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) label = defaultLabel;\n  return {\n    id: id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\nexport { $313b98861ee5dd6c$export$d6875122194c7b44 as useLabels };", "map": {"version": 3, "names": ["$313b98861ee5dd6c$export$d6875122194c7b44", "props", "defaultLabel", "id", "label", "labelledBy", "$bdb11010cef70236$export$f680877a34711e37", "ids", "Set", "trim", "split", "join"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useLabels.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {useId} from './useId';\n\n/**\n * Merges aria-label and aria-labelledby into aria-labelledby when both exist.\n * @param props - Aria label props.\n * @param defaultLabel - Default value for aria-label when not present.\n */\nexport function useLabels(props: DOMProps & AriaLabelingProps, defaultLabel?: string): DOMProps & AriaLabelingProps {\n  let {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = useId(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) {\n    labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  }\n\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) {\n    label = defaultLabel;\n  }\n\n  return {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAoBO,SAASA,0CAAUC,KAAmC,EAAEC,YAAqB;EAClF,IAAI;IAAAC,EAAA,EACFA,EAAE;IACF,cAAcC,KAAK;IACnB,mBAAmBC;EAAU,CAC9B,GAAGJ,KAAA;EAEJ;EACA;EACAE,EAAA,GAAK,IAAAG,yCAAI,EAAEH,EAAA;EACX,IAAIE,UAAA,IAAcD,KAAA,EAAO;IACvB,IAAIG,GAAA,GAAM,IAAIC,GAAA,CAAI,CAACL,EAAA,E,GAAOE,UAAA,CAAWI,IAAI,GAAGC,KAAK,CAAC,OAAO;IACzDL,UAAA,GAAa,C,GAAIE,GAAA,CAAI,CAACI,IAAI,CAAC;EAC7B,OAAO,IAAIN,UAAA,EACTA,UAAA,GAAaA,UAAA,CAAWI,IAAI,GAAGC,KAAK,CAAC,OAAOC,IAAI,CAAC;EAGnD;EACA,IAAI,CAACP,KAAA,IAAS,CAACC,UAAA,IAAcH,YAAA,EAC3BE,KAAA,GAAQF,YAAA;EAGV,OAAO;QACLC,EAAA;IACA,cAAcC,KAAA;IACd,mBAAmBC;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}