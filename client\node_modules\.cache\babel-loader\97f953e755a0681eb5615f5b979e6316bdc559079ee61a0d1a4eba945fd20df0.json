{"ast": null, "code": "import { useCallback as n } from \"react\";\nfunction l(e, r) {\n  return e !== null && r !== null && typeof e == \"object\" && typeof r == \"object\" && \"id\" in e && \"id\" in r ? e.id === r.id : e === r;\n}\nfunction u(e = l) {\n  return n((r, t) => {\n    if (typeof e == \"string\") {\n      let o = e;\n      return (r == null ? void 0 : r[o]) === (t == null ? void 0 : t[o]);\n    }\n    return e(r, t);\n  }, [e]);\n}\nexport { u as useByComparator };", "map": {"version": 3, "names": ["useCallback", "n", "l", "e", "r", "id", "u", "t", "o", "useByComparator"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-by-comparator.js"], "sourcesContent": ["import{useCallback as n}from\"react\";function l(e,r){return e!==null&&r!==null&&typeof e==\"object\"&&typeof r==\"object\"&&\"id\"in e&&\"id\"in r?e.id===r.id:e===r}function u(e=l){return n((r,t)=>{if(typeof e==\"string\"){let o=e;return(r==null?void 0:r[o])===(t==null?void 0:t[o])}return e(r,t)},[e])}export{u as useByComparator};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,KAAG,IAAI,IAAEC,CAAC,KAAG,IAAI,IAAE,OAAOD,CAAC,IAAE,QAAQ,IAAE,OAAOC,CAAC,IAAE,QAAQ,IAAE,IAAI,IAAGD,CAAC,IAAE,IAAI,IAAGC,CAAC,GAACD,CAAC,CAACE,EAAE,KAAGD,CAAC,CAACC,EAAE,GAACF,CAAC,KAAGC,CAAC;AAAA;AAAC,SAASE,CAACA,CAACH,CAAC,GAACD,CAAC,EAAC;EAAC,OAAOD,CAAC,CAAC,CAACG,CAAC,EAACG,CAAC,KAAG;IAAC,IAAG,OAAOJ,CAAC,IAAE,QAAQ,EAAC;MAAC,IAAIK,CAAC,GAACL,CAAC;MAAC,OAAM,CAACC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,CAAC,CAAC,OAAKD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACC,CAAC,CAAC,CAAC;IAAA;IAAC,OAAOL,CAAC,CAACC,CAAC,EAACG,CAAC,CAAC;EAAA,CAAC,EAAC,CAACJ,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOG,CAAC,IAAIG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}