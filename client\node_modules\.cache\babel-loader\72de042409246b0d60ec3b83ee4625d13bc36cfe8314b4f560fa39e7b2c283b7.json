{"ast": null, "code": "/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Custom event names for updating the autocomplete's aria-activedecendant.\nconst $5671b20cf9b562b2$export$447a38995de2c711 = 'react-aria-clear-focus';\nconst $5671b20cf9b562b2$export$831c820ad60f9d12 = 'react-aria-focus';\nexport { $5671b20cf9b562b2$export$447a38995de2c711 as CLEAR_FOCUS_EVENT, $5671b20cf9b562b2$export$831c820ad60f9d12 as FOCUS_EVENT };", "map": {"version": 3, "names": ["$5671b20cf9b562b2$export$447a38995de2c711", "$5671b20cf9b562b2$export$831c820ad60f9d12"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\constants.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Custom event names for updating the autocomplete's aria-activedecendant.\nexport const CLEAR_FOCUS_EVENT = 'react-aria-clear-focus';\nexport const FOCUS_EVENT = 'react-aria-focus';\n"], "mappings": "AAAA;;;;;;;;;;GAAA,CAYA;AACO,MAAMA,yCAAA,GAAoB;AAC1B,MAAMC,yCAAA,GAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}