{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\setting\\\\client\\\\src\\\\components\\\\settings\\\\AppearanceSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { SunIcon, MoonIcon, ComputerDesktopIcon, SwatchIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst AppearanceSettings = ({\n  settings,\n  onUpdate\n}) => {\n  _s();\n  var _dateFormats$find, _timeFormats$find, _themes$find, _colors$find, _fontSizes$find;\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    const newData = {\n      ...formData,\n      [name]: value\n    };\n    setFormData(newData);\n  };\n  const handleToggle = setting => {\n    const newData = {\n      ...formData,\n      [setting]: !formData[setting]\n    };\n    setFormData(newData);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n  const themes = [{\n    value: 'light',\n    label: 'Light',\n    icon: SunIcon,\n    description: 'Clean and bright interface'\n  }, {\n    value: 'dark',\n    label: 'Dark',\n    icon: MoonIcon,\n    description: 'Easy on the eyes in low light'\n  }, {\n    value: 'auto',\n    label: 'Auto',\n    icon: ComputerDesktopIcon,\n    description: 'Matches your system preference'\n  }];\n  const colors = [{\n    value: '#3B82F6',\n    label: 'Blue',\n    color: 'bg-blue-500'\n  }, {\n    value: '#10B981',\n    label: 'Green',\n    color: 'bg-green-500'\n  }, {\n    value: '#8B5CF6',\n    label: 'Purple',\n    color: 'bg-purple-500'\n  }, {\n    value: '#F59E0B',\n    label: 'Yellow',\n    color: 'bg-yellow-500'\n  }, {\n    value: '#EF4444',\n    label: 'Red',\n    color: 'bg-red-500'\n  }, {\n    value: '#6B7280',\n    label: 'Gray',\n    color: 'bg-gray-500'\n  }];\n  const fontSizes = [{\n    value: 'small',\n    label: 'Small',\n    description: 'Compact text size'\n  }, {\n    value: 'medium',\n    label: 'Medium',\n    description: 'Default text size'\n  }, {\n    value: 'large',\n    label: 'Large',\n    description: 'Larger text for better readability'\n  }];\n  const languages = [{\n    value: 'en',\n    label: 'English'\n  }, {\n    value: 'es',\n    label: 'Español'\n  }, {\n    value: 'fr',\n    label: 'Français'\n  }, {\n    value: 'de',\n    label: 'Deutsch'\n  }, {\n    value: 'it',\n    label: 'Italiano'\n  }, {\n    value: 'pt',\n    label: 'Português'\n  }];\n  const dateFormats = [{\n    value: 'MM/DD/YYYY',\n    label: 'MM/DD/YYYY',\n    example: '12/31/2024'\n  }, {\n    value: 'DD/MM/YYYY',\n    label: 'DD/MM/YYYY',\n    example: '31/12/2024'\n  }, {\n    value: 'YYYY-MM-DD',\n    label: 'YYYY-MM-DD',\n    example: '2024-12-31'\n  }, {\n    value: 'DD MMM YYYY',\n    label: 'DD MMM YYYY',\n    example: '31 Dec 2024'\n  }];\n  const timeFormats = [{\n    value: '12h',\n    label: '12-hour',\n    example: '2:30 PM'\n  }, {\n    value: '24h',\n    label: '24-hour',\n    example: '14:30'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Appearance Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: \"Customize the look and feel of your interface.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Theme\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-4 sm:grid-cols-3\",\n          children: themes.map(theme => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"theme\",\n              value: theme.value,\n              id: `theme-${theme.value}`,\n              checked: formData.theme === theme.value,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"sr-only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: `theme-${theme.value}`,\n              className: classNames('relative flex cursor-pointer rounded-lg border p-4 focus:outline-none', formData.theme === theme.value ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300', !isEditing && 'cursor-not-allowed opacity-50'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(theme.icon, {\n                  className: \"h-6 w-6 text-gray-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: theme.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: theme.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, theme.value, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(SwatchIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), \"Primary Color\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-3 sm:grid-cols-6\",\n          children: colors.map(color => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"primaryColor\",\n              value: color.value,\n              id: `color-${color.value}`,\n              checked: formData.primaryColor === color.value,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"sr-only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: `color-${color.value}`,\n              className: classNames('relative flex flex-col items-center cursor-pointer rounded-lg border p-3 focus:outline-none', formData.primaryColor === color.value ? 'border-gray-400 ring-2 ring-gray-400' : 'border-gray-300', !isEditing && 'cursor-not-allowed opacity-50'),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: classNames('w-8 h-8 rounded-full mb-2', color.color)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-700\",\n                children: color.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, color.value, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Typography & Layout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"fontSize\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Font Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: fontSizes.map(size => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"fontSize\",\n                  value: size.value,\n                  id: `fontSize-${size.value}`,\n                  checked: formData.fontSize === size.value,\n                  onChange: handleInputChange,\n                  disabled: !isEditing,\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `fontSize-${size.value}`,\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: size.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 ml-2\",\n                    children: size.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)]\n              }, size.value, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"Compact Mode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: \"Reduce spacing and padding for a more compact interface\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.compactMode || false,\n                onChange: () => handleToggle('compactMode'),\n                disabled: !isEditing,\n                className: classNames(formData.compactMode ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Compact mode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  \"aria-hidden\": \"true\",\n                  className: classNames(formData.compactMode ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"Show Avatars\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: \"Display user avatars throughout the interface\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.showAvatars || false,\n                onChange: () => handleToggle('showAvatars'),\n                disabled: !isEditing,\n                className: classNames(formData.showAvatars ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Show avatars\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  \"aria-hidden\": \"true\",\n                  className: classNames(formData.showAvatars ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Localization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"language\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"language\",\n              id: \"language\",\n              value: formData.language || 'en',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: languages.map(lang => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: lang.value,\n                children: lang.label\n              }, lang.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"dateFormat\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Date Format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"dateFormat\",\n              id: \"dateFormat\",\n              value: formData.dateFormat || 'MM/DD/YYYY',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: dateFormats.map(format => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: format.value,\n                children: format.label\n              }, format.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [\"Example: \", (_dateFormats$find = dateFormats.find(f => f.value === formData.dateFormat)) === null || _dateFormats$find === void 0 ? void 0 : _dateFormats$find.example]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"timeFormat\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Time Format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"timeFormat\",\n              id: \"timeFormat\",\n              value: formData.timeFormat || '12h',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: timeFormats.map(format => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: format.value,\n                children: format.label\n              }, format.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [\"Example: \", (_timeFormats$find = timeFormats.find(f => f.value === formData.timeFormat)) === null || _timeFormats$find === void 0 ? void 0 : _timeFormats$find.example]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(SwatchIcon, {\n              className: \"h-5 w-5 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: \"Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-blue-700\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Your interface will use the \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: (_themes$find = themes.find(t => t.value === formData.theme)) === null || _themes$find === void 0 ? void 0 : _themes$find.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 47\n                }, this), \" theme with \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: (_colors$find = colors.find(c => c.value === formData.primaryColor)) === null || _colors$find === void 0 ? void 0 : _colors$find.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 24\n                }, this), \" as the primary color and \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: (_fontSizes$find = fontSizes.find(f => f.value === formData.fontSize)) === null || _fontSizes$find === void 0 ? void 0 : _fontSizes$find.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 23\n                }, this), \" font size.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setIsEditing(true),\n          className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Edit Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(AppearanceSettings, \"rEZs2nirDlaVd6Cwh1oU/4Iupv4=\");\n_c = AppearanceSettings;\nexport default AppearanceSettings;\nvar _c;\n$RefreshReg$(_c, \"AppearanceSettings\");", "map": {"version": 3, "names": ["React", "useState", "Switch", "SunIcon", "MoonIcon", "ComputerDesktopIcon", "SwatchIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "AppearanceSettings", "settings", "onUpdate", "_s", "_dateFormats$find", "_timeFormats$find", "_themes$find", "_colors$find", "_fontSizes$find", "formData", "setFormData", "isEditing", "setIsEditing", "handleInputChange", "e", "name", "value", "target", "newData", "handleToggle", "setting", "handleSubmit", "preventDefault", "handleCancel", "themes", "label", "icon", "description", "colors", "color", "fontSizes", "languages", "dateFormats", "example", "timeFormats", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "map", "theme", "type", "id", "checked", "onChange", "disabled", "htmlFor", "primaryColor", "size", "fontSize", "compactMode", "showAvatars", "language", "lang", "dateFormat", "format", "find", "f", "timeFormat", "t", "c", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/src/components/settings/AppearanceSettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { \n  SunIcon, \n  MoonIcon, \n  ComputerDesktopIcon,\n  SwatchIcon \n} from '@heroicons/react/24/outline';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst AppearanceSettings = ({ settings, onUpdate }) => {\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    const newData = { ...formData, [name]: value };\n    setFormData(newData);\n  };\n\n  const handleToggle = (setting) => {\n    const newData = { ...formData, [setting]: !formData[setting] };\n    setFormData(newData);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n\n  const themes = [\n    { value: 'light', label: 'Light', icon: SunIcon, description: 'Clean and bright interface' },\n    { value: 'dark', label: 'Dark', icon: MoonIcon, description: 'Easy on the eyes in low light' },\n    { value: 'auto', label: 'Auto', icon: ComputerDesktopIcon, description: 'Matches your system preference' }\n  ];\n\n  const colors = [\n    { value: '#3B82F6', label: 'Blue', color: 'bg-blue-500' },\n    { value: '#10B981', label: 'Green', color: 'bg-green-500' },\n    { value: '#8B5CF6', label: 'Purple', color: 'bg-purple-500' },\n    { value: '#F59E0B', label: 'Yellow', color: 'bg-yellow-500' },\n    { value: '#EF4444', label: 'Red', color: 'bg-red-500' },\n    { value: '#6B7280', label: 'Gray', color: 'bg-gray-500' }\n  ];\n\n  const fontSizes = [\n    { value: 'small', label: 'Small', description: 'Compact text size' },\n    { value: 'medium', label: 'Medium', description: 'Default text size' },\n    { value: 'large', label: 'Large', description: 'Larger text for better readability' }\n  ];\n\n  const languages = [\n    { value: 'en', label: 'English' },\n    { value: 'es', label: 'Español' },\n    { value: 'fr', label: 'Français' },\n    { value: 'de', label: 'Deutsch' },\n    { value: 'it', label: 'Italiano' },\n    { value: 'pt', label: 'Português' }\n  ];\n\n  const dateFormats = [\n    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY', example: '12/31/2024' },\n    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY', example: '31/12/2024' },\n    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD', example: '2024-12-31' },\n    { value: 'DD MMM YYYY', label: 'DD MMM YYYY', example: '31 Dec 2024' }\n  ];\n\n  const timeFormats = [\n    { value: '12h', label: '12-hour', example: '2:30 PM' },\n    { value: '24h', label: '24-hour', example: '14:30' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Appearance Settings</h3>\n        <p className=\"text-sm text-gray-600\">\n          Customize the look and feel of your interface.\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit}>\n        {/* Theme Selection */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Theme</h4>\n          \n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n            {themes.map((theme) => (\n              <div key={theme.value}>\n                <input\n                  type=\"radio\"\n                  name=\"theme\"\n                  value={theme.value}\n                  id={`theme-${theme.value}`}\n                  checked={formData.theme === theme.value}\n                  onChange={handleInputChange}\n                  disabled={!isEditing}\n                  className=\"sr-only\"\n                />\n                <label\n                  htmlFor={`theme-${theme.value}`}\n                  className={classNames(\n                    'relative flex cursor-pointer rounded-lg border p-4 focus:outline-none',\n                    formData.theme === theme.value\n                      ? 'border-blue-500 ring-2 ring-blue-500'\n                      : 'border-gray-300',\n                    !isEditing && 'cursor-not-allowed opacity-50'\n                  )}\n                >\n                  <div className=\"flex items-center\">\n                    <theme.icon className=\"h-6 w-6 text-gray-600 mr-3\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{theme.label}</div>\n                      <div className=\"text-sm text-gray-500\">{theme.description}</div>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Primary Color */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <SwatchIcon className=\"h-5 w-5 mr-2\" />\n            Primary Color\n          </h4>\n          \n          <div className=\"grid grid-cols-3 gap-3 sm:grid-cols-6\">\n            {colors.map((color) => (\n              <div key={color.value}>\n                <input\n                  type=\"radio\"\n                  name=\"primaryColor\"\n                  value={color.value}\n                  id={`color-${color.value}`}\n                  checked={formData.primaryColor === color.value}\n                  onChange={handleInputChange}\n                  disabled={!isEditing}\n                  className=\"sr-only\"\n                />\n                <label\n                  htmlFor={`color-${color.value}`}\n                  className={classNames(\n                    'relative flex flex-col items-center cursor-pointer rounded-lg border p-3 focus:outline-none',\n                    formData.primaryColor === color.value\n                      ? 'border-gray-400 ring-2 ring-gray-400'\n                      : 'border-gray-300',\n                    !isEditing && 'cursor-not-allowed opacity-50'\n                  )}\n                >\n                  <div className={classNames('w-8 h-8 rounded-full mb-2', color.color)} />\n                  <div className=\"text-xs text-gray-700\">{color.label}</div>\n                </label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Typography & Layout */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Typography & Layout</h4>\n          \n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label htmlFor=\"fontSize\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Font Size\n              </label>\n              <div className=\"space-y-2\">\n                {fontSizes.map((size) => (\n                  <div key={size.value}>\n                    <input\n                      type=\"radio\"\n                      name=\"fontSize\"\n                      value={size.value}\n                      id={`fontSize-${size.value}`}\n                      checked={formData.fontSize === size.value}\n                      onChange={handleInputChange}\n                      disabled={!isEditing}\n                      className=\"mr-2\"\n                    />\n                    <label htmlFor={`fontSize-${size.value}`} className=\"text-sm\">\n                      <span className=\"font-medium\">{size.label}</span>\n                      <span className=\"text-gray-500 ml-2\">{size.description}</span>\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <h5 className=\"text-sm font-medium text-gray-900\">Compact Mode</h5>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    Reduce spacing and padding for a more compact interface\n                  </p>\n                </div>\n                <Switch\n                  checked={formData.compactMode || false}\n                  onChange={() => handleToggle('compactMode')}\n                  disabled={!isEditing}\n                  className={classNames(\n                    formData.compactMode ? 'bg-blue-600' : 'bg-gray-200',\n                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                    !isEditing && 'opacity-50 cursor-not-allowed'\n                  )}\n                >\n                  <span className=\"sr-only\">Compact mode</span>\n                  <span\n                    aria-hidden=\"true\"\n                    className={classNames(\n                      formData.compactMode ? 'translate-x-5' : 'translate-x-0',\n                      'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                    )}\n                  />\n                </Switch>\n              </div>\n\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <h5 className=\"text-sm font-medium text-gray-900\">Show Avatars</h5>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    Display user avatars throughout the interface\n                  </p>\n                </div>\n                <Switch\n                  checked={formData.showAvatars || false}\n                  onChange={() => handleToggle('showAvatars')}\n                  disabled={!isEditing}\n                  className={classNames(\n                    formData.showAvatars ? 'bg-blue-600' : 'bg-gray-200',\n                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                    !isEditing && 'opacity-50 cursor-not-allowed'\n                  )}\n                >\n                  <span className=\"sr-only\">Show avatars</span>\n                  <span\n                    aria-hidden=\"true\"\n                    className={classNames(\n                      formData.showAvatars ? 'translate-x-5' : 'translate-x-0',\n                      'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                    )}\n                  />\n                </Switch>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Localization */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Localization</h4>\n          \n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n            <div>\n              <label htmlFor=\"language\" className=\"block text-sm font-medium text-gray-700\">\n                Language\n              </label>\n              <select\n                name=\"language\"\n                id=\"language\"\n                value={formData.language || 'en'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {languages.map((lang) => (\n                  <option key={lang.value} value={lang.value}>\n                    {lang.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"dateFormat\" className=\"block text-sm font-medium text-gray-700\">\n                Date Format\n              </label>\n              <select\n                name=\"dateFormat\"\n                id=\"dateFormat\"\n                value={formData.dateFormat || 'MM/DD/YYYY'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {dateFormats.map((format) => (\n                  <option key={format.value} value={format.value}>\n                    {format.label}\n                  </option>\n                ))}\n              </select>\n              <p className=\"mt-1 text-xs text-gray-500\">\n                Example: {dateFormats.find(f => f.value === formData.dateFormat)?.example}\n              </p>\n            </div>\n\n            <div>\n              <label htmlFor=\"timeFormat\" className=\"block text-sm font-medium text-gray-700\">\n                Time Format\n              </label>\n              <select\n                name=\"timeFormat\"\n                id=\"timeFormat\"\n                value={formData.timeFormat || '12h'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {timeFormats.map((format) => (\n                  <option key={format.value} value={format.value}>\n                    {format.label}\n                  </option>\n                ))}\n              </select>\n              <p className=\"mt-1 text-xs text-gray-500\">\n                Example: {timeFormats.find(f => f.value === formData.timeFormat)?.example}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Preview */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <SwatchIcon className=\"h-5 w-5 text-blue-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800\">\n                Preview\n              </h3>\n              <div className=\"mt-2 text-sm text-blue-700\">\n                <p>\n                  Your interface will use the <strong>{themes.find(t => t.value === formData.theme)?.label}</strong> theme\n                  with <strong>{colors.find(c => c.value === formData.primaryColor)?.label}</strong> as the primary color\n                  and <strong>{fontSizes.find(f => f.value === formData.fontSize)?.label}</strong> font size.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3\">\n          {isEditing ? (\n            <>\n              <button\n                type=\"button\"\n                onClick={handleCancel}\n                className=\"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Save Changes\n              </button>\n            </>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={() => setIsEditing(true)}\n              className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Edit Settings\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AppearanceSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SACEC,OAAO,EACPC,QAAQ,EACRC,mBAAmB,EACnBC,UAAU,QACL,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,eAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAACgB,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM4B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,MAAMC,OAAO,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACM,IAAI,GAAGC;IAAM,CAAC;IAC9CN,WAAW,CAACQ,OAAO,CAAC;EACtB,CAAC;EAED,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMF,OAAO,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACW,OAAO,GAAG,CAACX,QAAQ,CAACW,OAAO;IAAE,CAAC;IAC9DV,WAAW,CAACQ,OAAO,CAAC;EACtB,CAAC;EAED,MAAMG,YAAY,GAAIP,CAAC,IAAK;IAC1BA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBpB,QAAQ,CAACO,QAAQ,CAAC;IAClBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzBb,WAAW,CAACT,QAAQ,CAAC;IACrBW,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMY,MAAM,GAAG,CACb;IAAER,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAEvC,OAAO;IAAEwC,WAAW,EAAE;EAA6B,CAAC,EAC5F;IAAEX,KAAK,EAAE,MAAM;IAAES,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAEtC,QAAQ;IAAEuC,WAAW,EAAE;EAAgC,CAAC,EAC9F;IAAEX,KAAK,EAAE,MAAM;IAAES,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAErC,mBAAmB;IAAEsC,WAAW,EAAE;EAAiC,CAAC,CAC3G;EAED,MAAMC,MAAM,GAAG,CACb;IAAEZ,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,MAAM;IAAEI,KAAK,EAAE;EAAc,CAAC,EACzD;IAAEb,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,OAAO;IAAEI,KAAK,EAAE;EAAe,CAAC,EAC3D;IAAEb,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,QAAQ;IAAEI,KAAK,EAAE;EAAgB,CAAC,EAC7D;IAAEb,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,QAAQ;IAAEI,KAAK,EAAE;EAAgB,CAAC,EAC7D;IAAEb,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,KAAK;IAAEI,KAAK,EAAE;EAAa,CAAC,EACvD;IAAEb,KAAK,EAAE,SAAS;IAAES,KAAK,EAAE,MAAM;IAAEI,KAAK,EAAE;EAAc,CAAC,CAC1D;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEd,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE,OAAO;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpE;IAAEX,KAAK,EAAE,QAAQ;IAAES,KAAK,EAAE,QAAQ;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACtE;IAAEX,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE,OAAO;IAAEE,WAAW,EAAE;EAAqC,CAAC,CACtF;EAED,MAAMI,SAAS,GAAG,CAChB;IAAEf,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAU,CAAC,EACjC;IAAET,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAU,CAAC,EACjC;IAAET,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAW,CAAC,EAClC;IAAET,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAU,CAAC,EACjC;IAAET,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAW,CAAC,EAClC;IAAET,KAAK,EAAE,IAAI;IAAES,KAAK,EAAE;EAAY,CAAC,CACpC;EAED,MAAMO,WAAW,GAAG,CAClB;IAAEhB,KAAK,EAAE,YAAY;IAAES,KAAK,EAAE,YAAY;IAAEQ,OAAO,EAAE;EAAa,CAAC,EACnE;IAAEjB,KAAK,EAAE,YAAY;IAAES,KAAK,EAAE,YAAY;IAAEQ,OAAO,EAAE;EAAa,CAAC,EACnE;IAAEjB,KAAK,EAAE,YAAY;IAAES,KAAK,EAAE,YAAY;IAAEQ,OAAO,EAAE;EAAa,CAAC,EACnE;IAAEjB,KAAK,EAAE,aAAa;IAAES,KAAK,EAAE,aAAa;IAAEQ,OAAO,EAAE;EAAc,CAAC,CACvE;EAED,MAAMC,WAAW,GAAG,CAClB;IAAElB,KAAK,EAAE,KAAK;IAAES,KAAK,EAAE,SAAS;IAAEQ,OAAO,EAAE;EAAU,CAAC,EACtD;IAAEjB,KAAK,EAAE,KAAK;IAAES,KAAK,EAAE,SAAS;IAAEQ,OAAO,EAAE;EAAQ,CAAC,CACrD;EAED,oBACEzC,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAI2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/EhD,OAAA;QAAG2C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhD,OAAA;MAAMiD,QAAQ,EAAEpB,YAAa;MAAAe,QAAA,gBAE3B5C,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnEhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDZ,MAAM,CAACkB,GAAG,CAAEC,KAAK,iBAChBnD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cACEoD,IAAI,EAAC,OAAO;cACZ7B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE2B,KAAK,CAAC3B,KAAM;cACnB6B,EAAE,EAAE,SAASF,KAAK,CAAC3B,KAAK,EAAG;cAC3B8B,OAAO,EAAErC,QAAQ,CAACkC,KAAK,KAAKA,KAAK,CAAC3B,KAAM;cACxC+B,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFhD,OAAA;cACEyD,OAAO,EAAE,SAASN,KAAK,CAAC3B,KAAK,EAAG;cAChCmB,SAAS,EAAExC,UAAU,CACnB,uEAAuE,EACvEc,QAAQ,CAACkC,KAAK,KAAKA,KAAK,CAAC3B,KAAK,GAC1B,sCAAsC,GACtC,iBAAiB,EACrB,CAACL,SAAS,IAAI,+BAChB,CAAE;cAAAyB,QAAA,eAEF5C,OAAA;gBAAK2C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5C,OAAA,CAACmD,KAAK,CAACjB,IAAI;kBAACS,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDhD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAK2C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEO,KAAK,CAAClB;kBAAK;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEhD,OAAA;oBAAK2C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEO,KAAK,CAAChB;kBAAW;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GA5BAG,KAAK,CAAC3B,KAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACF,UAAU;YAAC6C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDR,MAAM,CAACc,GAAG,CAAEb,KAAK,iBAChBrC,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cACEoD,IAAI,EAAC,OAAO;cACZ7B,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEa,KAAK,CAACb,KAAM;cACnB6B,EAAE,EAAE,SAAShB,KAAK,CAACb,KAAK,EAAG;cAC3B8B,OAAO,EAAErC,QAAQ,CAACyC,YAAY,KAAKrB,KAAK,CAACb,KAAM;cAC/C+B,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFhD,OAAA;cACEyD,OAAO,EAAE,SAASpB,KAAK,CAACb,KAAK,EAAG;cAChCmB,SAAS,EAAExC,UAAU,CACnB,6FAA6F,EAC7Fc,QAAQ,CAACyC,YAAY,KAAKrB,KAAK,CAACb,KAAK,GACjC,sCAAsC,GACtC,iBAAiB,EACrB,CAACL,SAAS,IAAI,+BAChB,CAAE;cAAAyB,QAAA,gBAEF5C,OAAA;gBAAK2C,SAAS,EAAExC,UAAU,CAAC,2BAA2B,EAAEkC,KAAK,CAACA,KAAK;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxEhD,OAAA;gBAAK2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEP,KAAK,CAACJ;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA,GAvBAX,KAAK,CAACb,KAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEjFhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOyD,OAAO,EAAC,UAAU;cAACd,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBN,SAAS,CAACY,GAAG,CAAES,IAAI,iBAClB3D,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBACEoD,IAAI,EAAC,OAAO;kBACZ7B,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEmC,IAAI,CAACnC,KAAM;kBAClB6B,EAAE,EAAE,YAAYM,IAAI,CAACnC,KAAK,EAAG;kBAC7B8B,OAAO,EAAErC,QAAQ,CAAC2C,QAAQ,KAAKD,IAAI,CAACnC,KAAM;kBAC1C+B,QAAQ,EAAElC,iBAAkB;kBAC5BmC,QAAQ,EAAE,CAACrC,SAAU;kBACrBwB,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFhD,OAAA;kBAAOyD,OAAO,EAAE,YAAYE,IAAI,CAACnC,KAAK,EAAG;kBAACmB,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAC3D5C,OAAA;oBAAM2C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEe,IAAI,CAAC1B;kBAAK;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjDhD,OAAA;oBAAM2C,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEe,IAAI,CAACxB;kBAAW;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA,GAdAW,IAAI,CAACnC,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAef,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C5C,OAAA;gBAAK2C,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB5C,OAAA;kBAAI2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEhD,OAAA;kBAAG2C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNhD,OAAA,CAACN,MAAM;gBACL4D,OAAO,EAAErC,QAAQ,CAAC4C,WAAW,IAAI,KAAM;gBACvCN,QAAQ,EAAEA,CAAA,KAAM5B,YAAY,CAAC,aAAa,CAAE;gBAC5C6B,QAAQ,EAAE,CAACrC,SAAU;gBACrBwB,SAAS,EAAExC,UAAU,CACnBc,QAAQ,CAAC4C,WAAW,GAAG,aAAa,GAAG,aAAa,EACpD,wNAAwN,EACxN,CAAC1C,SAAS,IAAI,+BAChB,CAAE;gBAAAyB,QAAA,gBAEF5C,OAAA;kBAAM2C,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ChD,OAAA;kBACE,eAAY,MAAM;kBAClB2C,SAAS,EAAExC,UAAU,CACnBc,QAAQ,CAAC4C,WAAW,GAAG,eAAe,GAAG,eAAe,EACxD,4HACF;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhD,OAAA;cAAK2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C5C,OAAA;gBAAK2C,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB5C,OAAA;kBAAI2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEhD,OAAA;kBAAG2C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNhD,OAAA,CAACN,MAAM;gBACL4D,OAAO,EAAErC,QAAQ,CAAC6C,WAAW,IAAI,KAAM;gBACvCP,QAAQ,EAAEA,CAAA,KAAM5B,YAAY,CAAC,aAAa,CAAE;gBAC5C6B,QAAQ,EAAE,CAACrC,SAAU;gBACrBwB,SAAS,EAAExC,UAAU,CACnBc,QAAQ,CAAC6C,WAAW,GAAG,aAAa,GAAG,aAAa,EACpD,wNAAwN,EACxN,CAAC3C,SAAS,IAAI,+BAChB,CAAE;gBAAAyB,QAAA,gBAEF5C,OAAA;kBAAM2C,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ChD,OAAA;kBACE,eAAY,MAAM;kBAClB2C,SAAS,EAAExC,UAAU,CACnBc,QAAQ,CAAC6C,WAAW,GAAG,eAAe,GAAG,eAAe,EACxD,4HACF;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1EhD,OAAA;UAAK2C,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOyD,OAAO,EAAC,UAAU;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEuB,IAAI,EAAC,UAAU;cACf8B,EAAE,EAAC,UAAU;cACb7B,KAAK,EAAEP,QAAQ,CAAC8C,QAAQ,IAAI,IAAK;cACjCR,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKL,SAAS,CAACW,GAAG,CAAEc,IAAI,iBAClBhE,OAAA;gBAAyBwB,KAAK,EAAEwC,IAAI,CAACxC,KAAM;gBAAAoB,QAAA,EACxCoB,IAAI,CAAC/B;cAAK,GADA+B,IAAI,CAACxC,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOyD,OAAO,EAAC,YAAY;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEhF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEuB,IAAI,EAAC,YAAY;cACjB8B,EAAE,EAAC,YAAY;cACf7B,KAAK,EAAEP,QAAQ,CAACgD,UAAU,IAAI,YAAa;cAC3CV,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKJ,WAAW,CAACU,GAAG,CAAEgB,MAAM,iBACtBlE,OAAA;gBAA2BwB,KAAK,EAAE0C,MAAM,CAAC1C,KAAM;gBAAAoB,QAAA,EAC5CsB,MAAM,CAACjC;cAAK,GADFiC,MAAM,CAAC1C,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACThD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,GAAAhC,iBAAA,GAAC4B,WAAW,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAKP,QAAQ,CAACgD,UAAU,CAAC,cAAArD,iBAAA,uBAAtDA,iBAAA,CAAwD6B,OAAO;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOyD,OAAO,EAAC,YAAY;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEhF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEuB,IAAI,EAAC,YAAY;cACjB8B,EAAE,EAAC,YAAY;cACf7B,KAAK,EAAEP,QAAQ,CAACoD,UAAU,IAAI,KAAM;cACpCd,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKF,WAAW,CAACQ,GAAG,CAAEgB,MAAM,iBACtBlE,OAAA;gBAA2BwB,KAAK,EAAE0C,MAAM,CAAC1C,KAAM;gBAAAoB,QAAA,EAC5CsB,MAAM,CAACjC;cAAK,GADFiC,MAAM,CAAC1C,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACThD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,GAAA/B,iBAAA,GAAC6B,WAAW,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAKP,QAAQ,CAACoD,UAAU,CAAC,cAAAxD,iBAAA,uBAAtDA,iBAAA,CAAwD4B,OAAO;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE5C,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5C,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5C,OAAA,CAACF,UAAU;cAAC6C,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5C,OAAA;cAAI2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhD,OAAA;cAAK2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzC5C,OAAA;gBAAA4C,QAAA,GAAG,8BAC2B,eAAA5C,OAAA;kBAAA4C,QAAA,GAAA9B,YAAA,GAASkB,MAAM,CAACmC,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKP,QAAQ,CAACkC,KAAK,CAAC,cAAArC,YAAA,uBAA5CA,YAAA,CAA8CmB;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,gBAC7F,eAAAhD,OAAA;kBAAA4C,QAAA,GAAA7B,YAAA,GAASqB,MAAM,CAAC+B,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC/C,KAAK,KAAKP,QAAQ,CAACyC,YAAY,CAAC,cAAA3C,YAAA,uBAAnDA,YAAA,CAAqDkB;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,8BAC9E,eAAAhD,OAAA;kBAAA4C,QAAA,GAAA5B,eAAA,GAASsB,SAAS,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAKP,QAAQ,CAAC2C,QAAQ,CAAC,cAAA5C,eAAA,uBAAlDA,eAAA,CAAoDiB;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAClF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxCzB,SAAS,gBACRnB,OAAA,CAAAE,SAAA;UAAA0C,QAAA,gBACE5C,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACboB,OAAO,EAAEzC,YAAa;YACtBY,SAAS,EAAC,2LAA2L;YAAAC,QAAA,EACtM;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,+LAA+L;YAAAC,QAAA,EAC1M;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHhD,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACboB,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,IAAI,CAAE;UAClCuB,SAAS,EAAC,+LAA+L;UAAAC,QAAA,EAC1M;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CApXIH,kBAAkB;AAAAiE,EAAA,GAAlBjE,kBAAkB;AAsXxB,eAAeA,kBAAkB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}