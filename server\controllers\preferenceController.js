const Preference = require('../models/Preference');

// GET user preferences
const getPreferences = async (req, res) => {
  try {
    const { userId } = req.params;
    
    let preferences = await Preference.findOne({ userId });
    
    // Create default preferences if none exist
    if (!preferences) {
      preferences = new Preference({
        userId,
        theme: 'light',
        language: 'en'
      });
      await preferences.save();
    }
    
    res.json({ 
      success: true, 
      data: preferences 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// PUT update preferences
const updatePreferences = async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;
    
    let preferences = await Preference.findOneAndUpdate(
      { userId }, 
      updateData, 
      { new: true, upsert: true, runValidators: true }
    );
    
    res.json({ 
      success: true, 
      message: 'Preferences updated successfully',
      data: preferences 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// GET all themes
const getAvailableThemes = async (req, res) => {
  try {
    const themes = [
      { value: 'light', label: 'Light Theme', description: 'Clean and bright interface' },
      { value: 'dark', label: 'Dark Theme', description: 'Easy on the eyes in low light' }
    ];
    
    res.json({ 
      success: true, 
      data: themes 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// GET available languages
const getAvailableLanguages = async (req, res) => {
  try {
    const languages = [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'it', name: 'Italiano' },
      { code: 'pt', name: 'Português' }
    ];
    
    res.json({ 
      success: true, 
      data: languages 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

module.exports = {
  getPreferences,
  updatePreferences,
  getAvailableThemes,
  getAvailableLanguages
};
