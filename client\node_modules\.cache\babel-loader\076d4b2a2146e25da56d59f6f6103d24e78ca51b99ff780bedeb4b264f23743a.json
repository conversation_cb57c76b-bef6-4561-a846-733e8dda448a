{"ast": null, "code": "\"use client\";\n\nimport { useFocusRing as P } from \"@react-aria/focus\";\nimport { useHover as c } from \"@react-aria/interactions\";\nimport { useMemo as g } from \"react\";\nimport { useId as v } from '../../hooks/use-id.js';\nimport { useDisabled as A } from '../../internal/disabled.js';\nimport { useProvidedId as _ } from '../../internal/id.js';\nimport { forwardRefWithAs as R, mergeProps as D, useRender as E } from '../../utils/render.js';\nimport { useDescribedBy as F } from '../description/description.js';\nimport { useLabelledBy as U } from '../label/label.js';\nlet x = \"input\";\nfunction h(p, s) {\n  let a = v(),\n    l = _(),\n    i = A(),\n    {\n      id: d = l || `headlessui-input-${a}`,\n      disabled: e = i || !1,\n      autoFocus: o = !1,\n      invalid: t = !1,\n      ...u\n    } = p,\n    f = U(),\n    m = F(),\n    {\n      isFocused: r,\n      focusProps: T\n    } = P({\n      autoFocus: o\n    }),\n    {\n      isHovered: n,\n      hoverProps: b\n    } = c({\n      isDisabled: e\n    }),\n    y = D({\n      ref: s,\n      id: d,\n      \"aria-labelledby\": f,\n      \"aria-describedby\": m,\n      \"aria-invalid\": t ? \"true\" : void 0,\n      disabled: e || void 0,\n      autoFocus: o\n    }, T, b),\n    I = g(() => ({\n      disabled: e,\n      invalid: t,\n      hover: n,\n      focus: r,\n      autofocus: o\n    }), [e, t, n, r, o]);\n  return E()({\n    ourProps: y,\n    theirProps: u,\n    slot: I,\n    defaultTag: x,\n    name: \"Input\"\n  });\n}\nlet S = R(h);\nexport { S as Input };", "map": {"version": 3, "names": ["useFocusRing", "P", "useHover", "c", "useMemo", "g", "useId", "v", "useDisabled", "A", "useProvidedId", "_", "forwardRefWithAs", "R", "mergeProps", "D", "useRender", "E", "useDescribedBy", "F", "useLabelledBy", "U", "x", "h", "p", "s", "a", "l", "i", "id", "d", "disabled", "e", "autoFocus", "o", "invalid", "t", "u", "f", "m", "isFocused", "r", "focusProps", "T", "isHovered", "n", "hoverProps", "b", "isDisabled", "y", "ref", "I", "hover", "focus", "autofocus", "ourProps", "theirProps", "slot", "defaultTag", "name", "S", "Input"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/components/input/input.js"], "sourcesContent": ["\"use client\";import{useFocusRing as P}from\"@react-aria/focus\";import{useHover as c}from\"@react-aria/interactions\";import{useMemo as g}from\"react\";import{useId as v}from'../../hooks/use-id.js';import{useDisabled as A}from'../../internal/disabled.js';import{useProvidedId as _}from'../../internal/id.js';import{forwardRefWithAs as R,mergeProps as D,useRender as E}from'../../utils/render.js';import{useDescribedBy as F}from'../description/description.js';import{useLabelledBy as U}from'../label/label.js';let x=\"input\";function h(p,s){let a=v(),l=_(),i=A(),{id:d=l||`headlessui-input-${a}`,disabled:e=i||!1,autoFocus:o=!1,invalid:t=!1,...u}=p,f=U(),m=F(),{isFocused:r,focusProps:T}=P({autoFocus:o}),{isHovered:n,hoverProps:b}=c({isDisabled:e}),y=D({ref:s,id:d,\"aria-labelledby\":f,\"aria-describedby\":m,\"aria-invalid\":t?\"true\":void 0,disabled:e||void 0,autoFocus:o},T,b),I=g(()=>({disabled:e,invalid:t,hover:n,focus:r,autofocus:o}),[e,t,n,r,o]);return E()({ourProps:y,theirProps:u,slot:I,defaultTag:x,name:\"Input\"})}let S=R(h);export{S as Input};\n"], "mappings": "AAAA,YAAY;;AAAC,SAAOA,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAAC,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,CAAC;IAACoB,CAAC,GAAChB,CAAC,CAAC,CAAC;IAACiB,CAAC,GAACnB,CAAC,CAAC,CAAC;IAAC;MAACoB,EAAE,EAACC,CAAC,GAACH,CAAC,IAAE,oBAAoBD,CAAC,EAAE;MAACK,QAAQ,EAACC,CAAC,GAACJ,CAAC,IAAE,CAAC,CAAC;MAACK,SAAS,EAACC,CAAC,GAAC,CAAC,CAAC;MAACC,OAAO,EAACC,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGC;IAAC,CAAC,GAACb,CAAC;IAACc,CAAC,GAACjB,CAAC,CAAC,CAAC;IAACkB,CAAC,GAACpB,CAAC,CAAC,CAAC;IAAC;MAACqB,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC1C,CAAC,CAAC;MAACgC,SAAS,EAACC;IAAC,CAAC,CAAC;IAAC;MAACU,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC5C,CAAC,CAAC;MAAC6C,UAAU,EAAChB;IAAC,CAAC,CAAC;IAACiB,CAAC,GAAClC,CAAC,CAAC;MAACmC,GAAG,EAACzB,CAAC;MAACI,EAAE,EAACC,CAAC;MAAC,iBAAiB,EAACQ,CAAC;MAAC,kBAAkB,EAACC,CAAC;MAAC,cAAc,EAACH,CAAC,GAAC,MAAM,GAAC,KAAK,CAAC;MAACL,QAAQ,EAACC,CAAC,IAAE,KAAK,CAAC;MAACC,SAAS,EAACC;IAAC,CAAC,EAACS,CAAC,EAACI,CAAC,CAAC;IAACI,CAAC,GAAC9C,CAAC,CAAC,OAAK;MAAC0B,QAAQ,EAACC,CAAC;MAACG,OAAO,EAACC,CAAC;MAACgB,KAAK,EAACP,CAAC;MAACQ,KAAK,EAACZ,CAAC;MAACa,SAAS,EAACpB;IAAC,CAAC,CAAC,EAAC,CAACF,CAAC,EAACI,CAAC,EAACS,CAAC,EAACJ,CAAC,EAACP,CAAC,CAAC,CAAC;EAAC,OAAOjB,CAAC,CAAC,CAAC,CAAC;IAACsC,QAAQ,EAACN,CAAC;IAACO,UAAU,EAACnB,CAAC;IAACoB,IAAI,EAACN,CAAC;IAACO,UAAU,EAACpC,CAAC;IAACqC,IAAI,EAAC;EAAO,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC/C,CAAC,CAACU,CAAC,CAAC;AAAC,SAAOqC,CAAC,IAAIC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}