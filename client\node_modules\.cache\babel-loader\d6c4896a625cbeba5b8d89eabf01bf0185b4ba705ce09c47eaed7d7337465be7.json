{"ast": null, "code": "import s, { useState as c } from \"react\";\nimport { useIsMounted as m } from '../hooks/use-is-mounted.js';\nimport { Hidden as f, HiddenFeatures as l } from './hidden.js';\nfunction b({\n  onFocus: n\n}) {\n  let [r, o] = c(!0),\n    u = m();\n  return r ? s.createElement(f, {\n    as: \"button\",\n    type: \"button\",\n    features: l.Focusable,\n    onFocus: a => {\n      a.preventDefault();\n      let e,\n        i = 50;\n      function t() {\n        if (i-- <= 0) {\n          e && cancelAnimationFrame(e);\n          return;\n        }\n        if (n()) {\n          if (cancelAnimationFrame(e), !u.current) return;\n          o(!1);\n          return;\n        }\n        e = requestAnimationFrame(t);\n      }\n      e = requestAnimationFrame(t);\n    }\n  }) : null;\n}\nexport { b as FocusSentinel };", "map": {"version": 3, "names": ["s", "useState", "c", "useIsMounted", "m", "Hidden", "f", "HiddenFeatures", "l", "b", "onFocus", "n", "r", "o", "u", "createElement", "as", "type", "features", "Focusable", "a", "preventDefault", "e", "i", "t", "cancelAnimationFrame", "current", "requestAnimationFrame", "FocusSentinel"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/internal/focus-sentinel.js"], "sourcesContent": ["import s,{useState as c}from\"react\";import{useIsMounted as m}from'../hooks/use-is-mounted.js';import{Hidden as f,HiddenFeatures as l}from'./hidden.js';function b({onFocus:n}){let[r,o]=c(!0),u=m();return r?s.createElement(f,{as:\"button\",type:\"button\",features:l.Focusable,onFocus:a=>{a.preventDefault();let e,i=50;function t(){if(i--<=0){e&&cancelAnimationFrame(e);return}if(n()){if(cancelAnimationFrame(e),!u.current)return;o(!1);return}e=requestAnimationFrame(t)}e=requestAnimationFrame(t)}}):null}export{b as FocusSentinel};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,aAAa;AAAC,SAASC,CAACA,CAAC;EAACC,OAAO,EAACC;AAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACX,CAAC,CAAC,CAAC,CAAC,CAAC;IAACY,CAAC,GAACV,CAAC,CAAC,CAAC;EAAC,OAAOQ,CAAC,GAACZ,CAAC,CAACe,aAAa,CAACT,CAAC,EAAC;IAACU,EAAE,EAAC,QAAQ;IAACC,IAAI,EAAC,QAAQ;IAACC,QAAQ,EAACV,CAAC,CAACW,SAAS;IAACT,OAAO,EAACU,CAAC,IAAE;MAACA,CAAC,CAACC,cAAc,CAAC,CAAC;MAAC,IAAIC,CAAC;QAACC,CAAC,GAAC,EAAE;MAAC,SAASC,CAACA,CAAA,EAAE;QAAC,IAAGD,CAAC,EAAE,IAAE,CAAC,EAAC;UAACD,CAAC,IAAEG,oBAAoB,CAACH,CAAC,CAAC;UAAC;QAAM;QAAC,IAAGX,CAAC,CAAC,CAAC,EAAC;UAAC,IAAGc,oBAAoB,CAACH,CAAC,CAAC,EAAC,CAACR,CAAC,CAACY,OAAO,EAAC;UAAOb,CAAC,CAAC,CAAC,CAAC,CAAC;UAAC;QAAM;QAACS,CAAC,GAACK,qBAAqB,CAACH,CAAC,CAAC;MAAA;MAACF,CAAC,GAACK,qBAAqB,CAACH,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC,GAAC,IAAI;AAAA;AAAC,SAAOf,CAAC,IAAImB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}