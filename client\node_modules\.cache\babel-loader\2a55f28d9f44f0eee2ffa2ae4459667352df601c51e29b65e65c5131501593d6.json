{"ast": null, "code": "import { useSyncExternalStore as e } from \"react\";\nfunction o(t) {\n  return e(t.subscribe, t.getSnapshot, t.getSnapshot);\n}\nexport { o as useStore };", "map": {"version": 3, "names": ["useSyncExternalStore", "e", "o", "t", "subscribe", "getSnapshot", "useStore"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-store.js"], "sourcesContent": ["import{useSyncExternalStore as e}from\"react\";function o(t){return e(t.subscribe,t.getSnapshot,t.getSnapshot)}export{o as useStore};\n"], "mappings": "AAAA,SAAOA,oBAAoB,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACE,CAAC,CAACC,SAAS,EAACD,CAAC,CAACE,WAAW,EAACF,CAAC,CAACE,WAAW,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAII,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}