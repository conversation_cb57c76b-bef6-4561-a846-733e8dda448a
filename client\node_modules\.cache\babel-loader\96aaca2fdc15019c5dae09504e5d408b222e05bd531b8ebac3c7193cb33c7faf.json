{"ast": null, "code": "import { useEffect as f, useLayoutEffect as c } from \"react\";\nimport { env as i } from '../utils/env.js';\nlet n = (e, t) => {\n  i.isServer ? f(e, t) : c(e, t);\n};\nexport { n as useIsoMorphicEffect };", "map": {"version": 3, "names": ["useEffect", "f", "useLayoutEffect", "c", "env", "i", "n", "e", "t", "isServer", "useIsoMorphicEffect"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js"], "sourcesContent": ["import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,iBAAiB;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,KAAG;EAACH,CAAC,CAACI,QAAQ,GAACR,CAAC,CAACM,CAAC,EAACC,CAAC,CAAC,GAACL,CAAC,CAACI,CAAC,EAACC,CAAC,CAAC;AAAA,CAAC;AAAC,SAAOF,CAAC,IAAII,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}