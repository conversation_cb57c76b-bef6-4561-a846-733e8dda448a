{"ast": null, "code": "import { useRef as o, useState as f } from \"react\";\nimport { useEvent as a } from './use-event.js';\nfunction T(l, r, c) {\n  let [i, s] = f(c),\n    e = l !== void 0,\n    t = o(e),\n    u = o(!1),\n    d = o(!1);\n  return e && !t.current && !u.current ? (u.current = !0, t.current = e, console.error(\"A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.\")) : !e && t.current && !d.current && (d.current = !0, t.current = e, console.error(\"A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.\")), [e ? l : i, a(n => (e || s(n), r == null ? void 0 : r(n)))];\n}\nexport { T as useControllable };", "map": {"version": 3, "names": ["useRef", "o", "useState", "f", "useEvent", "a", "T", "l", "r", "c", "i", "s", "e", "t", "u", "d", "current", "console", "error", "n", "useControllable"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-controllable.js"], "sourcesContent": ["import{useRef as o,useState as f}from\"react\";import{useEvent as a}from'./use-event.js';function T(l,r,c){let[i,s]=f(c),e=l!==void 0,t=o(e),u=o(!1),d=o(!1);return e&&!t.current&&!u.current?(u.current=!0,t.current=e,console.error(\"A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.\")):!e&&t.current&&!d.current&&(d.current=!0,t.current=e,console.error(\"A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.\")),[e?l:i,a(n=>(e||s(n),r==null?void 0:r(n)))]}export{T as useControllable};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACR,CAAC,CAACM,CAAC,CAAC;IAACG,CAAC,GAACL,CAAC,KAAG,KAAK,CAAC;IAACM,CAAC,GAACZ,CAAC,CAACW,CAAC,CAAC;IAACE,CAAC,GAACb,CAAC,CAAC,CAAC,CAAC,CAAC;IAACc,CAAC,GAACd,CAAC,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOW,CAAC,IAAE,CAACC,CAAC,CAACG,OAAO,IAAE,CAACF,CAAC,CAACE,OAAO,IAAEF,CAAC,CAACE,OAAO,GAAC,CAAC,CAAC,EAACH,CAAC,CAACG,OAAO,GAACJ,CAAC,EAACK,OAAO,CAACC,KAAK,CAAC,+JAA+J,CAAC,IAAE,CAACN,CAAC,IAAEC,CAAC,CAACG,OAAO,IAAE,CAACD,CAAC,CAACC,OAAO,KAAGD,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,EAACH,CAAC,CAACG,OAAO,GAACJ,CAAC,EAACK,OAAO,CAACC,KAAK,CAAC,+JAA+J,CAAC,CAAC,EAAC,CAACN,CAAC,GAACL,CAAC,GAACG,CAAC,EAACL,CAAC,CAACc,CAAC,KAAGP,CAAC,IAAED,CAAC,CAACQ,CAAC,CAAC,EAACX,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOb,CAAC,IAAIc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}