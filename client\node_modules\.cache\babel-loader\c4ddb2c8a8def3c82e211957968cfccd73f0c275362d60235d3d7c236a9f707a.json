{"ast": null, "code": "import { createContext as r, useContext as a, useMemo as m } from \"react\";\nimport { useOnUnmount as c } from '../../hooks/use-on-unmount.js';\nimport { ComboboxMachine as i } from './combobox-machine.js';\nconst u = r(null);\nfunction p(n) {\n  let o = a(u);\n  if (o === null) {\n    let e = new Error(`<${n} /> is missing a parent <Combobox /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(e, b), e;\n  }\n  return o;\n}\nfunction b({\n  id: n,\n  virtual: o = null,\n  __demoMode: e = !1\n}) {\n  let t = m(() => i.new({\n    id: n,\n    virtual: o,\n    __demoMode: e\n  }), []);\n  return c(() => t.dispose()), t;\n}\nexport { u as ComboboxContext, b as useComboboxMachine, p as useComboboxMachineContext };", "map": {"version": 3, "names": ["createContext", "r", "useContext", "a", "useMemo", "m", "useOnUnmount", "c", "ComboboxMachine", "i", "u", "p", "n", "o", "e", "Error", "captureStackTrace", "b", "id", "virtual", "__demoMode", "t", "new", "dispose", "ComboboxContext", "useComboboxMachine", "useComboboxMachineContext"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/components/combobox/combobox-machine-glue.js"], "sourcesContent": ["import{createContext as r,useContext as a,useMemo as m}from\"react\";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{ComboboxMachine as i}from'./combobox-machine.js';const u=r(null);function p(n){let o=a(u);if(o===null){let e=new Error(`<${n} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,b),e}return o}function b({id:n,virtual:o=null,__demoMode:e=!1}){let t=m(()=>i.new({id:n,virtual:o,__demoMode:e}),[]);return c(()=>t.dispose()),t}export{u as ComboboxContext,b as useComboboxMachine,p as useComboboxMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,uBAAuB;AAAC,MAAMC,CAAC,GAACT,CAAC,CAAC,IAAI,CAAC;AAAC,SAASU,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACO,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,IAAIH,CAAC,iDAAiD,CAAC;IAAC,MAAMG,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACG,CAAC,CAAC,EAACH,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAC;EAACC,EAAE,EAACN,CAAC;EAACO,OAAO,EAACN,CAAC,GAAC,IAAI;EAACO,UAAU,EAACN,CAAC,GAAC,CAAC;AAAC,CAAC,EAAC;EAAC,IAAIO,CAAC,GAAChB,CAAC,CAAC,MAAII,CAAC,CAACa,GAAG,CAAC;IAACJ,EAAE,EAACN,CAAC;IAACO,OAAO,EAACN,CAAC;IAACO,UAAU,EAACN;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;EAAC,OAAOP,CAAC,CAAC,MAAIc,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAACF,CAAC;AAAA;AAAC,SAAOX,CAAC,IAAIc,eAAe,EAACP,CAAC,IAAIQ,kBAAkB,EAACd,CAAC,IAAIe,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}