{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\setting\\\\client\\\\src\\\\components\\\\settings\\\\EventSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst EventSettings = ({\n  settings,\n  onUpdate\n}) => {\n  _s();\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    const newValue = type === 'number' ? parseInt(value) : value;\n    const newData = {\n      ...formData,\n      [name]: newValue\n    };\n    setFormData(newData);\n  };\n  const handleToggle = setting => {\n    const newData = {\n      ...formData,\n      [setting]: !formData[setting]\n    };\n    setFormData(newData);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n  const timeZones = ['America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles', 'Europe/London', 'Europe/Paris', 'Asia/Tokyo', 'UTC'];\n  const eventTypes = [{\n    value: 'public',\n    label: 'Public'\n  }, {\n    value: 'private',\n    label: 'Private'\n  }, {\n    value: 'invite-only',\n    label: 'Invite Only'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Event Management Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: \"Configure default settings for event creation and management.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Default Event Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"defaultEventDuration\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Default Duration (minutes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"defaultEventDuration\",\n              id: \"defaultEventDuration\",\n              value: formData.defaultEventDuration || 60,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              min: \"15\",\n              max: \"1440\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"defaultCapacity\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Default Capacity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"defaultCapacity\",\n              id: \"defaultCapacity\",\n              value: formData.defaultCapacity || 100,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              min: \"1\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"defaultTimeZone\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Default Time Zone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"defaultTimeZone\",\n              id: \"defaultTimeZone\",\n              value: formData.defaultTimeZone || 'UTC',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: timeZones.map(tz => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: tz,\n                children: tz.replace('_', ' ')\n              }, tz, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"defaultEventType\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Default Event Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"defaultEventType\",\n              id: \"defaultEventType\",\n              value: formData.defaultEventType || 'public',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: eventTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: type.value,\n                children: type.label\n              }, type.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Registration Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Auto-approve Registrations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Automatically approve event registrations without manual review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.autoApproveRegistrations || false,\n              onChange: () => handleToggle('autoApproveRegistrations'),\n              disabled: !isEditing,\n              className: classNames(formData.autoApproveRegistrations ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Auto-approve registrations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.autoApproveRegistrations ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Allow Waitlist\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Enable waitlist when events reach capacity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.allowWaitlist || false,\n              onChange: () => handleToggle('allowWaitlist'),\n              disabled: !isEditing,\n              className: classNames(formData.allowWaitlist ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Allow waitlist\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.allowWaitlist ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Send Confirmation Emails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Automatically send confirmation emails to registered attendees\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.sendConfirmationEmails || false,\n              onChange: () => handleToggle('sendConfirmationEmails'),\n              disabled: !isEditing,\n              className: classNames(formData.sendConfirmationEmails ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Send confirmation emails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.sendConfirmationEmails ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Require Approval\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Require manual approval for all event registrations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.requireApproval || false,\n              onChange: () => handleToggle('requireApproval'),\n              disabled: !isEditing,\n              className: classNames(formData.requireApproval ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Require approval\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.requireApproval ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setIsEditing(true),\n          className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Edit Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(EventSettings, \"rEZs2nirDlaVd6Cwh1oU/4Iupv4=\");\n_c = EventSettings;\nexport default EventSettings;\nvar _c;\n$RefreshReg$(_c, \"EventSettings\");", "map": {"version": 3, "names": ["React", "useState", "Switch", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "EventSettings", "settings", "onUpdate", "_s", "formData", "setFormData", "isEditing", "setIsEditing", "handleInputChange", "e", "name", "value", "type", "target", "newValue", "parseInt", "newData", "handleToggle", "setting", "handleSubmit", "preventDefault", "handleCancel", "timeZones", "eventTypes", "label", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "defaultEventDuration", "onChange", "disabled", "min", "max", "defaultCapacity", "defaultTimeZone", "map", "tz", "replace", "defaultEventType", "checked", "autoApproveRegistrations", "allowWaitlist", "sendConfirmationEmails", "requireApproval", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/src/components/settings/EventSettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst EventSettings = ({ settings, onUpdate }) => {\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    const newValue = type === 'number' ? parseInt(value) : value;\n    const newData = { ...formData, [name]: newValue };\n    setFormData(newData);\n  };\n\n  const handleToggle = (setting) => {\n    const newData = { ...formData, [setting]: !formData[setting] };\n    setFormData(newData);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n\n  const timeZones = [\n    'America/New_York',\n    'America/Chicago',\n    'America/Denver',\n    'America/Los_Angeles',\n    'Europe/London',\n    'Europe/Paris',\n    'Asia/Tokyo',\n    'UTC'\n  ];\n\n  const eventTypes = [\n    { value: 'public', label: 'Public' },\n    { value: 'private', label: 'Private' },\n    { value: 'invite-only', label: 'Invite Only' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Event Management Settings</h3>\n        <p className=\"text-sm text-gray-600\">\n          Configure default settings for event creation and management.\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit}>\n        {/* Default Event Settings */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Default Event Settings</h4>\n          \n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label htmlFor=\"defaultEventDuration\" className=\"block text-sm font-medium text-gray-700\">\n                Default Duration (minutes)\n              </label>\n              <input\n                type=\"number\"\n                name=\"defaultEventDuration\"\n                id=\"defaultEventDuration\"\n                value={formData.defaultEventDuration || 60}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                min=\"15\"\n                max=\"1440\"\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"defaultCapacity\" className=\"block text-sm font-medium text-gray-700\">\n                Default Capacity\n              </label>\n              <input\n                type=\"number\"\n                name=\"defaultCapacity\"\n                id=\"defaultCapacity\"\n                value={formData.defaultCapacity || 100}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                min=\"1\"\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"defaultTimeZone\" className=\"block text-sm font-medium text-gray-700\">\n                Default Time Zone\n              </label>\n              <select\n                name=\"defaultTimeZone\"\n                id=\"defaultTimeZone\"\n                value={formData.defaultTimeZone || 'UTC'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {timeZones.map((tz) => (\n                  <option key={tz} value={tz}>\n                    {tz.replace('_', ' ')}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"defaultEventType\" className=\"block text-sm font-medium text-gray-700\">\n                Default Event Type\n              </label>\n              <select\n                name=\"defaultEventType\"\n                id=\"defaultEventType\"\n                value={formData.defaultEventType || 'public'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {eventTypes.map((type) => (\n                  <option key={type.value} value={type.value}>\n                    {type.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Registration Settings */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Registration Settings</h4>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Auto-approve Registrations</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Automatically approve event registrations without manual review\n                </p>\n              </div>\n              <Switch\n                checked={formData.autoApproveRegistrations || false}\n                onChange={() => handleToggle('autoApproveRegistrations')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.autoApproveRegistrations ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Auto-approve registrations</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.autoApproveRegistrations ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Allow Waitlist</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Enable waitlist when events reach capacity\n                </p>\n              </div>\n              <Switch\n                checked={formData.allowWaitlist || false}\n                onChange={() => handleToggle('allowWaitlist')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.allowWaitlist ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Allow waitlist</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.allowWaitlist ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Send Confirmation Emails</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Automatically send confirmation emails to registered attendees\n                </p>\n              </div>\n              <Switch\n                checked={formData.sendConfirmationEmails || false}\n                onChange={() => handleToggle('sendConfirmationEmails')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.sendConfirmationEmails ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Send confirmation emails</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.sendConfirmationEmails ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Require Approval</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Require manual approval for all event registrations\n                </p>\n              </div>\n              <Switch\n                checked={formData.requireApproval || false}\n                onChange={() => handleToggle('requireApproval')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.requireApproval ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Require approval</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.requireApproval ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3\">\n          {isEditing ? (\n            <>\n              <button\n                type=\"button\"\n                onClick={handleCancel}\n                className=\"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Save Changes\n              </button>\n            </>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={() => setIsEditing(true)}\n              className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Edit Settings\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default EventSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAACY,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGH,CAAC,CAACI,MAAM;IACtC,MAAMC,QAAQ,GAAGF,IAAI,KAAK,QAAQ,GAAGG,QAAQ,CAACJ,KAAK,CAAC,GAAGA,KAAK;IAC5D,MAAMK,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACM,IAAI,GAAGI;IAAS,CAAC;IACjDT,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMF,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACc,OAAO,GAAG,CAACd,QAAQ,CAACc,OAAO;IAAE,CAAC;IAC9Db,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMG,YAAY,GAAIV,CAAC,IAAK;IAC1BA,CAAC,CAACW,cAAc,CAAC,CAAC;IAClBlB,QAAQ,CAACE,QAAQ,CAAC;IAClBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzBhB,WAAW,CAACJ,QAAQ,CAAC;IACrBM,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMe,SAAS,GAAG,CAChB,kBAAkB,EAClB,iBAAiB,EACjB,gBAAgB,EAChB,qBAAqB,EACrB,eAAe,EACf,cAAc,EACd,YAAY,EACZ,KAAK,CACN;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEZ,KAAK,EAAE,QAAQ;IAAEa,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEb,KAAK,EAAE,SAAS;IAAEa,KAAK,EAAE;EAAU,CAAC,EACtC;IAAEb,KAAK,EAAE,aAAa;IAAEa,KAAK,EAAE;EAAc,CAAC,CAC/C;EAED,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBlC,OAAA;MAAAkC,QAAA,gBACElC,OAAA;QAAIiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrFtC,OAAA;QAAGiC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENtC,OAAA;MAAMuC,QAAQ,EAAEZ,YAAa;MAAAO,QAAA,gBAE3BlC,OAAA;QAAKiC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ClC,OAAA;UAAIiC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEpFtC,OAAA;UAAKiC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOwC,OAAO,EAAC,sBAAsB;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cACEoB,IAAI,EAAC,QAAQ;cACbF,IAAI,EAAC,sBAAsB;cAC3BuB,EAAE,EAAC,sBAAsB;cACzBtB,KAAK,EAAEP,QAAQ,CAAC8B,oBAAoB,IAAI,EAAG;cAC3CC,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrB+B,GAAG,EAAC,IAAI;cACRC,GAAG,EAAC,MAAM;cACVb,SAAS,EAAC;YAAwJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOwC,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cACEoB,IAAI,EAAC,QAAQ;cACbF,IAAI,EAAC,iBAAiB;cACtBuB,EAAE,EAAC,iBAAiB;cACpBtB,KAAK,EAAEP,QAAQ,CAACmC,eAAe,IAAI,GAAI;cACvCJ,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrB+B,GAAG,EAAC,GAAG;cACPZ,SAAS,EAAC;YAAwJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOwC,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cACEkB,IAAI,EAAC,iBAAiB;cACtBuB,EAAE,EAAC,iBAAiB;cACpBtB,KAAK,EAAEP,QAAQ,CAACoC,eAAe,IAAI,KAAM;cACzCL,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKJ,SAAS,CAACmB,GAAG,CAAEC,EAAE,iBAChBlD,OAAA;gBAAiBmB,KAAK,EAAE+B,EAAG;gBAAAhB,QAAA,EACxBgB,EAAE,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC,GADVD,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEP,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOwC,OAAO,EAAC,kBAAkB;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEtF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtC,OAAA;cACEkB,IAAI,EAAC,kBAAkB;cACvBuB,EAAE,EAAC,kBAAkB;cACrBtB,KAAK,EAAEP,QAAQ,CAACwC,gBAAgB,IAAI,QAAS;cAC7CT,QAAQ,EAAE3B,iBAAkB;cAC5B4B,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKH,UAAU,CAACkB,GAAG,CAAE7B,IAAI,iBACnBpB,OAAA;gBAAyBmB,KAAK,EAAEC,IAAI,CAACD,KAAM;gBAAAe,QAAA,EACxCd,IAAI,CAACY;cAAK,GADAZ,IAAI,CAACD,KAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QAAKiC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ClC,OAAA;UAAIiC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnFtC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA;YAAKiC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBlC,OAAA;gBAAIiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFtC,OAAA;gBAAGiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtC,OAAA,CAACF,MAAM;cACLuD,OAAO,EAAEzC,QAAQ,CAAC0C,wBAAwB,IAAI,KAAM;cACpDX,QAAQ,EAAEA,CAAA,KAAMlB,YAAY,CAAC,0BAA0B,CAAE;cACzDmB,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC0C,wBAAwB,GAAG,aAAa,GAAG,aAAa,EACjE,wNAAwN,EACxN,CAACxC,SAAS,IAAI,+BAChB,CAAE;cAAAoB,QAAA,gBAEFlC,OAAA;gBAAMiC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DtC,OAAA;gBACE,eAAY,MAAM;gBAClBiC,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC0C,wBAAwB,GAAG,eAAe,GAAG,eAAe,EACrE,4HACF;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBlC,OAAA;gBAAIiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEtC,OAAA;gBAAGiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtC,OAAA,CAACF,MAAM;cACLuD,OAAO,EAAEzC,QAAQ,CAAC2C,aAAa,IAAI,KAAM;cACzCZ,QAAQ,EAAEA,CAAA,KAAMlB,YAAY,CAAC,eAAe,CAAE;cAC9CmB,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC2C,aAAa,GAAG,aAAa,GAAG,aAAa,EACtD,wNAAwN,EACxN,CAACzC,SAAS,IAAI,+BAChB,CAAE;cAAAoB,QAAA,gBAEFlC,OAAA;gBAAMiC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CtC,OAAA;gBACE,eAAY,MAAM;gBAClBiC,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC2C,aAAa,GAAG,eAAe,GAAG,eAAe,EAC1D,4HACF;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBlC,OAAA;gBAAIiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EtC,OAAA;gBAAGiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtC,OAAA,CAACF,MAAM;cACLuD,OAAO,EAAEzC,QAAQ,CAAC4C,sBAAsB,IAAI,KAAM;cAClDb,QAAQ,EAAEA,CAAA,KAAMlB,YAAY,CAAC,wBAAwB,CAAE;cACvDmB,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC4C,sBAAsB,GAAG,aAAa,GAAG,aAAa,EAC/D,wNAAwN,EACxN,CAAC1C,SAAS,IAAI,+BAChB,CAAE;cAAAoB,QAAA,gBAEFlC,OAAA;gBAAMiC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtC,OAAA;gBACE,eAAY,MAAM;gBAClBiC,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC4C,sBAAsB,GAAG,eAAe,GAAG,eAAe,EACnE,4HACF;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBlC,OAAA;gBAAIiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEtC,OAAA;gBAAGiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtC,OAAA,CAACF,MAAM;cACLuD,OAAO,EAAEzC,QAAQ,CAAC6C,eAAe,IAAI,KAAM;cAC3Cd,QAAQ,EAAEA,CAAA,KAAMlB,YAAY,CAAC,iBAAiB,CAAE;cAChDmB,QAAQ,EAAE,CAAC9B,SAAU;cACrBmB,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC6C,eAAe,GAAG,aAAa,GAAG,aAAa,EACxD,wNAAwN,EACxN,CAAC3C,SAAS,IAAI,+BAChB,CAAE;cAAAoB,QAAA,gBAEFlC,OAAA;gBAAMiC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDtC,OAAA;gBACE,eAAY,MAAM;gBAClBiC,SAAS,EAAE9B,UAAU,CACnBS,QAAQ,CAAC6C,eAAe,GAAG,eAAe,GAAG,eAAe,EAC5D,4HACF;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QAAKiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxCpB,SAAS,gBACRd,OAAA,CAAAE,SAAA;UAAAgC,QAAA,gBACElC,OAAA;YACEoB,IAAI,EAAC,QAAQ;YACbsC,OAAO,EAAE7B,YAAa;YACtBI,SAAS,EAAC,2LAA2L;YAAAC,QAAA,EACtM;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACEoB,IAAI,EAAC,QAAQ;YACba,SAAS,EAAC,+LAA+L;YAAAC,QAAA,EAC1M;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHtC,OAAA;UACEoB,IAAI,EAAC,QAAQ;UACbsC,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,IAAI,CAAE;UAClCkB,SAAS,EAAC,+LAA+L;UAAAC,QAAA,EAC1M;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA5RIH,aAAa;AAAAmD,EAAA,GAAbnD,aAAa;AA8RnB,eAAeA,aAAa;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}