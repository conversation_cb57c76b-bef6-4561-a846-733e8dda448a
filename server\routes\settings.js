const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');

// Import controllers
const userController = require('../controllers/userController');
const notificationController = require('../controllers/notificationController');
const eventController = require('../controllers/eventController');
const profitController = require('../controllers/profitController');
const authController = require('../controllers/authController');
const preferenceController = require('../controllers/preferenceController');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/avatars/');
  },
  filename: (req, file, cb) => {
    cb(null, `avatar-${Date.now()}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// ===== PROFILE ROUTES =====
// GET user profile
router.get('/profile/:id', userController.getProfile);

// PUT update profile
router.put('/profile/:id', userController.updateProfile);

// POST upload avatar
router.post('/profile/:id/avatar', upload.single('avatar'), async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const User = require('../models/User');
    const user = await User.findByIdAndUpdate(
      id,
      { avatar: `/uploads/avatars/${req.file.filename}` },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: { avatar: user.avatar }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// ===== NOTIFICATION ROUTES =====
// GET user notifications
router.get('/notifications/:userId', notificationController.getUserNotifications);

// PUT mark notification as read
router.put('/notifications/:id/mark-read', notificationController.markAsRead);

// POST create notification
router.post('/notifications', notificationController.createNotification);

// ===== EVENT ROUTES =====
// GET all events
router.get('/events', eventController.getAllEvents);

// POST create event
router.post('/events', eventController.createEvent);

// PUT update event
router.put('/events/:id', eventController.updateEvent);

// DELETE event
router.delete('/events/:id', eventController.deleteEvent);

// ===== PROFIT ROUTES =====
// GET profit by event
router.get('/profit/event/:eventId', profitController.getProfitByEvent);

// POST record profit
router.post('/profit', profitController.recordProfit);

// GET total profit
router.get('/profit/total', profitController.getTotalProfit);

// GET profit analytics
router.get('/profit/analytics', profitController.getProfitAnalytics);

// ===== SECURITY ROUTES =====
// PUT change password
router.put('/security/change-password', authController.changePassword);

// PUT toggle 2FA
router.put('/security/2fa', authController.toggle2FA);

// GET security settings
router.get('/security/:userId', authController.getSecuritySettings);

// PUT update security settings
router.put('/security/:userId', authController.updateSecuritySettings);

// ===== APPEARANCE/PREFERENCE ROUTES =====
// GET user preferences
router.get('/preferences/:userId', preferenceController.getPreferences);

// PUT update preferences
router.put('/preferences/:userId', preferenceController.updatePreferences);

// GET available themes
router.get('/preferences/themes/available', preferenceController.getAvailableThemes);

// GET available languages
router.get('/preferences/languages/available', preferenceController.getAvailableLanguages);

module.exports = router;
