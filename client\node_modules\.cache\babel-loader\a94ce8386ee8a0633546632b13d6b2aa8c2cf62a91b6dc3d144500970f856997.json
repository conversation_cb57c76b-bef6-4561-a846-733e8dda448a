{"ast": null, "code": "var S = Object.defineProperty;\nvar I = (t, i, e) => i in t ? S(t, i, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: e\n}) : t[i] = e;\nvar c = (t, i, e) => (I(t, typeof i != \"symbol\" ? i + \"\" : i, e), e);\nimport { Machine as h } from '../../machine.js';\nimport { ActionTypes as R, stackMachines as A } from '../../machines/stack-machine.js';\nimport { Focus as f, calculateActiveIndex as x } from '../../utils/calculate-active-index.js';\nimport { sortByDomNode as E } from '../../utils/focus-management.js';\nimport { match as g } from '../../utils/match.js';\nvar C = (e => (e[e.Open = 0] = \"Open\", e[e.Closed = 1] = \"Closed\", e))(C || {}),\n  M = (e => (e[e.Single = 0] = \"Single\", e[e.Multi = 1] = \"Multi\", e))(M || {}),\n  F = (n => (n[n.Pointer = 0] = \"Pointer\", n[n.Focus = 1] = \"Focus\", n[n.Other = 2] = \"Other\", n))(F || {}),\n  _ = (l => (l[l.OpenCombobox = 0] = \"OpenCombobox\", l[l.CloseCombobox = 1] = \"CloseCombobox\", l[l.GoToOption = 2] = \"GoToOption\", l[l.SetTyping = 3] = \"SetTyping\", l[l.RegisterOption = 4] = \"RegisterOption\", l[l.UnregisterOption = 5] = \"UnregisterOption\", l[l.DefaultToFirstOption = 6] = \"DefaultToFirstOption\", l[l.SetActivationTrigger = 7] = \"SetActivationTrigger\", l[l.UpdateVirtualConfiguration = 8] = \"UpdateVirtualConfiguration\", l[l.SetInputElement = 9] = \"SetInputElement\", l[l.SetButtonElement = 10] = \"SetButtonElement\", l[l.SetOptionsElement = 11] = \"SetOptionsElement\", l))(_ || {});\nfunction T(t, i = e => e) {\n  let e = t.activeOptionIndex !== null ? t.options[t.activeOptionIndex] : null,\n    n = i(t.options.slice()),\n    o = n.length > 0 && n[0].dataRef.current.order !== null ? n.sort((u, a) => u.dataRef.current.order - a.dataRef.current.order) : E(n, u => u.dataRef.current.domRef.current),\n    r = e ? o.indexOf(e) : null;\n  return r === -1 && (r = null), {\n    options: o,\n    activeOptionIndex: r\n  };\n}\nlet D = {\n  [1](t) {\n    var i;\n    return (i = t.dataRef.current) != null && i.disabled || t.comboboxState === 1 ? t : {\n      ...t,\n      activeOptionIndex: null,\n      comboboxState: 1,\n      isTyping: !1,\n      activationTrigger: 2,\n      __demoMode: !1\n    };\n  },\n  [0](t) {\n    var i, e;\n    if ((i = t.dataRef.current) != null && i.disabled || t.comboboxState === 0) return t;\n    if ((e = t.dataRef.current) != null && e.value) {\n      let n = t.dataRef.current.calculateIndex(t.dataRef.current.value);\n      if (n !== -1) return {\n        ...t,\n        activeOptionIndex: n,\n        comboboxState: 0,\n        __demoMode: !1\n      };\n    }\n    return {\n      ...t,\n      comboboxState: 0,\n      __demoMode: !1\n    };\n  },\n  [3](t, i) {\n    return t.isTyping === i.isTyping ? t : {\n      ...t,\n      isTyping: i.isTyping\n    };\n  },\n  [2](t, i) {\n    var r, u, a, d;\n    if ((r = t.dataRef.current) != null && r.disabled || t.optionsElement && !((u = t.dataRef.current) != null && u.optionsPropsRef.current.static) && t.comboboxState === 1) return t;\n    if (t.virtual) {\n      let {\n          options: p,\n          disabled: s\n        } = t.virtual,\n        b = i.focus === f.Specific ? i.idx : x(i, {\n          resolveItems: () => p,\n          resolveActiveIndex: () => {\n            var v, m;\n            return (m = (v = t.activeOptionIndex) != null ? v : p.findIndex(y => !s(y))) != null ? m : null;\n          },\n          resolveDisabled: s,\n          resolveId() {\n            throw new Error(\"Function not implemented.\");\n          }\n        }),\n        l = (a = i.trigger) != null ? a : 2;\n      return t.activeOptionIndex === b && t.activationTrigger === l ? t : {\n        ...t,\n        activeOptionIndex: b,\n        activationTrigger: l,\n        isTyping: !1,\n        __demoMode: !1\n      };\n    }\n    let e = T(t);\n    if (e.activeOptionIndex === null) {\n      let p = e.options.findIndex(s => !s.dataRef.current.disabled);\n      p !== -1 && (e.activeOptionIndex = p);\n    }\n    let n = i.focus === f.Specific ? i.idx : x(i, {\n        resolveItems: () => e.options,\n        resolveActiveIndex: () => e.activeOptionIndex,\n        resolveId: p => p.id,\n        resolveDisabled: p => p.dataRef.current.disabled\n      }),\n      o = (d = i.trigger) != null ? d : 2;\n    return t.activeOptionIndex === n && t.activationTrigger === o ? t : {\n      ...t,\n      ...e,\n      isTyping: !1,\n      activeOptionIndex: n,\n      activationTrigger: o,\n      __demoMode: !1\n    };\n  },\n  [4]: (t, i) => {\n    var r, u, a, d;\n    if ((r = t.dataRef.current) != null && r.virtual) return {\n      ...t,\n      options: [...t.options, i.payload]\n    };\n    let e = i.payload,\n      n = T(t, p => (p.push(e), p));\n    t.activeOptionIndex === null && (a = (u = t.dataRef.current).isSelected) != null && a.call(u, i.payload.dataRef.current.value) && (n.activeOptionIndex = n.options.indexOf(e));\n    let o = {\n      ...t,\n      ...n,\n      activationTrigger: 2\n    };\n    return (d = t.dataRef.current) != null && d.__demoMode && t.dataRef.current.value === void 0 && (o.activeOptionIndex = 0), o;\n  },\n  [5]: (t, i) => {\n    var n;\n    if ((n = t.dataRef.current) != null && n.virtual) return {\n      ...t,\n      options: t.options.filter(o => o.id !== i.id)\n    };\n    let e = T(t, o => {\n      let r = o.findIndex(u => u.id === i.id);\n      return r !== -1 && o.splice(r, 1), o;\n    });\n    return {\n      ...t,\n      ...e,\n      activationTrigger: 2\n    };\n  },\n  [6]: (t, i) => t.defaultToFirstOption === i.value ? t : {\n    ...t,\n    defaultToFirstOption: i.value\n  },\n  [7]: (t, i) => t.activationTrigger === i.trigger ? t : {\n    ...t,\n    activationTrigger: i.trigger\n  },\n  [8]: (t, i) => {\n    var n, o;\n    if (t.virtual === null) return {\n      ...t,\n      virtual: {\n        options: i.options,\n        disabled: (n = i.disabled) != null ? n : () => !1\n      }\n    };\n    if (t.virtual.options === i.options && t.virtual.disabled === i.disabled) return t;\n    let e = t.activeOptionIndex;\n    if (t.activeOptionIndex !== null) {\n      let r = i.options.indexOf(t.virtual.options[t.activeOptionIndex]);\n      r !== -1 ? e = r : e = null;\n    }\n    return {\n      ...t,\n      activeOptionIndex: e,\n      virtual: {\n        options: i.options,\n        disabled: (o = i.disabled) != null ? o : () => !1\n      }\n    };\n  },\n  [9]: (t, i) => t.inputElement === i.element ? t : {\n    ...t,\n    inputElement: i.element\n  },\n  [10]: (t, i) => t.buttonElement === i.element ? t : {\n    ...t,\n    buttonElement: i.element\n  },\n  [11]: (t, i) => t.optionsElement === i.element ? t : {\n    ...t,\n    optionsElement: i.element\n  }\n};\nclass O extends h {\n  constructor(e) {\n    super(e);\n    c(this, \"actions\", {\n      onChange: e => {\n        let {\n          onChange: n,\n          compare: o,\n          mode: r,\n          value: u\n        } = this.state.dataRef.current;\n        return g(r, {\n          [0]: () => n == null ? void 0 : n(e),\n          [1]: () => {\n            let a = u.slice(),\n              d = a.findIndex(p => o(p, e));\n            return d === -1 ? a.push(e) : a.splice(d, 1), n == null ? void 0 : n(a);\n          }\n        });\n      },\n      registerOption: (e, n) => (this.send({\n        type: 4,\n        payload: {\n          id: e,\n          dataRef: n\n        }\n      }), () => {\n        this.state.activeOptionIndex === this.state.dataRef.current.calculateIndex(n.current.value) && this.send({\n          type: 6,\n          value: !0\n        }), this.send({\n          type: 5,\n          id: e\n        });\n      }),\n      goToOption: (e, n) => (this.send({\n        type: 6,\n        value: !1\n      }), this.send({\n        type: 2,\n        ...e,\n        trigger: n\n      })),\n      setIsTyping: e => {\n        this.send({\n          type: 3,\n          isTyping: e\n        });\n      },\n      closeCombobox: () => {\n        var e, n;\n        this.send({\n          type: 1\n        }), this.send({\n          type: 6,\n          value: !1\n        }), (n = (e = this.state.dataRef.current).onClose) == null || n.call(e);\n      },\n      openCombobox: () => {\n        this.send({\n          type: 0\n        }), this.send({\n          type: 6,\n          value: !0\n        });\n      },\n      setActivationTrigger: e => {\n        this.send({\n          type: 7,\n          trigger: e\n        });\n      },\n      selectActiveOption: () => {\n        let e = this.selectors.activeOptionIndex(this.state);\n        if (e !== null) {\n          if (this.actions.setIsTyping(!1), this.state.virtual) this.actions.onChange(this.state.virtual.options[e]);else {\n            let {\n              dataRef: n\n            } = this.state.options[e];\n            this.actions.onChange(n.current.value);\n          }\n          this.actions.goToOption({\n            focus: f.Specific,\n            idx: e\n          });\n        }\n      },\n      setInputElement: e => {\n        this.send({\n          type: 9,\n          element: e\n        });\n      },\n      setButtonElement: e => {\n        this.send({\n          type: 10,\n          element: e\n        });\n      },\n      setOptionsElement: e => {\n        this.send({\n          type: 11,\n          element: e\n        });\n      }\n    });\n    c(this, \"selectors\", {\n      activeDescendantId: e => {\n        var o, r;\n        let n = this.selectors.activeOptionIndex(e);\n        if (n !== null) return e.virtual ? (r = e.options.find(u => !u.dataRef.current.disabled && e.dataRef.current.compare(u.dataRef.current.value, e.virtual.options[n]))) == null ? void 0 : r.id : (o = e.options[n]) == null ? void 0 : o.id;\n      },\n      activeOptionIndex: e => {\n        if (e.defaultToFirstOption && e.activeOptionIndex === null && (e.virtual ? e.virtual.options.length > 0 : e.options.length > 0)) {\n          if (e.virtual) {\n            let {\n                options: o,\n                disabled: r\n              } = e.virtual,\n              u = o.findIndex(a => {\n                var d;\n                return !((d = r == null ? void 0 : r(a)) != null && d);\n              });\n            if (u !== -1) return u;\n          }\n          let n = e.options.findIndex(o => !o.dataRef.current.disabled);\n          if (n !== -1) return n;\n        }\n        return e.activeOptionIndex;\n      },\n      activeOption: e => {\n        var o, r;\n        let n = this.selectors.activeOptionIndex(e);\n        return n === null ? null : e.virtual ? e.virtual.options[n != null ? n : 0] : (r = (o = e.options[n]) == null ? void 0 : o.dataRef.current.value) != null ? r : null;\n      },\n      isActive: (e, n, o) => {\n        var u;\n        let r = this.selectors.activeOptionIndex(e);\n        return r === null ? !1 : e.virtual ? r === e.dataRef.current.calculateIndex(n) : ((u = e.options[r]) == null ? void 0 : u.id) === o;\n      },\n      shouldScrollIntoView: (e, n, o) => !(e.virtual || e.__demoMode || e.comboboxState !== 0 || e.activationTrigger === 0 || !this.selectors.isActive(e, n, o))\n    });\n    {\n      let n = this.state.id,\n        o = A.get(null);\n      this.disposables.add(o.on(R.Push, r => {\n        !o.selectors.isTop(r, n) && this.state.comboboxState === 0 && this.actions.closeCombobox();\n      })), this.on(0, () => o.actions.push(n)), this.on(1, () => o.actions.pop(n));\n    }\n  }\n  static new({\n    id: e,\n    virtual: n = null,\n    __demoMode: o = !1\n  }) {\n    var r;\n    return new O({\n      id: e,\n      dataRef: {\n        current: {}\n      },\n      comboboxState: o ? 0 : 1,\n      isTyping: !1,\n      options: [],\n      virtual: n ? {\n        options: n.options,\n        disabled: (r = n.disabled) != null ? r : () => !1\n      } : null,\n      activeOptionIndex: null,\n      activationTrigger: 2,\n      inputElement: null,\n      buttonElement: null,\n      optionsElement: null,\n      __demoMode: o\n    });\n  }\n  reduce(e, n) {\n    return g(n.type, D, e, n);\n  }\n}\nexport { _ as ActionTypes, F as ActivationTrigger, O as ComboboxMachine, C as ComboboxState, M as ValueMode };", "map": {"version": 3, "names": ["S", "Object", "defineProperty", "I", "t", "i", "e", "enumerable", "configurable", "writable", "value", "c", "Machine", "h", "ActionTypes", "R", "stackMachines", "A", "Focus", "f", "calculateActiveIndex", "x", "sortByDomNode", "E", "match", "g", "C", "Open", "Closed", "M", "Single", "Multi", "F", "n", "Pointer", "Other", "_", "l", "OpenCombobox", "CloseCombobox", "GoToOption", "SetTyping", "RegisterOption", "UnregisterOption", "DefaultToFirstOption", "SetActivationTrigger", "UpdateVirtualConfiguration", "SetInputElement", "SetButtonElement", "SetOptionsElement", "T", "activeOptionIndex", "options", "slice", "o", "length", "dataRef", "current", "order", "sort", "u", "a", "domRef", "r", "indexOf", "D", "disabled", "comboboxState", "isTyping", "activationTrigger", "__demoMode", "calculateIndex", "d", "optionsElement", "optionsPropsRef", "static", "virtual", "p", "s", "b", "focus", "Specific", "idx", "resolveItems", "resolveActiveIndex", "v", "m", "findIndex", "y", "resolveDisabled", "resolveId", "Error", "trigger", "id", "payload", "push", "isSelected", "call", "filter", "splice", "defaultToFirstOption", "inputElement", "element", "buttonElement", "O", "constructor", "onChange", "compare", "mode", "state", "registerOption", "send", "type", "goToOption", "setIsTyping", "closeCombobox", "onClose", "openCombobox", "setActivationTrigger", "selectActiveOption", "selectors", "actions", "setInputElement", "setButtonElement", "setOptionsElement", "activeDescendantId", "find", "activeOption", "isActive", "shouldScrollIntoView", "get", "disposables", "add", "on", "<PERSON><PERSON>", "isTop", "pop", "new", "reduce", "ActivationTrigger", "ComboboxMachine", "ComboboxState", "ValueMode"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/components/combobox/combobox-machine.js"], "sourcesContent": ["var S=Object.defineProperty;var I=(t,i,e)=>i in t?S(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var c=(t,i,e)=>(I(t,typeof i!=\"symbol\"?i+\"\":i,e),e);import{Machine as h}from'../../machine.js';import{ActionTypes as R,stackMachines as A}from'../../machines/stack-machine.js';import{Focus as f,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as E}from'../../utils/focus-management.js';import{match as g}from'../../utils/match.js';var C=(e=>(e[e.Open=0]=\"Open\",e[e.Closed=1]=\"Closed\",e))(C||{}),M=(e=>(e[e.Single=0]=\"Single\",e[e.Multi=1]=\"Multi\",e))(M||{}),F=(n=>(n[n.Pointer=0]=\"Pointer\",n[n.Focus=1]=\"Focus\",n[n.Other=2]=\"Other\",n))(F||{}),_=(l=>(l[l.OpenCombobox=0]=\"OpenCombobox\",l[l.CloseCombobox=1]=\"CloseCombobox\",l[l.GoToOption=2]=\"GoToOption\",l[l.SetTyping=3]=\"SetTyping\",l[l.RegisterOption=4]=\"RegisterOption\",l[l.UnregisterOption=5]=\"UnregisterOption\",l[l.DefaultToFirstOption=6]=\"DefaultToFirstOption\",l[l.SetActivationTrigger=7]=\"SetActivationTrigger\",l[l.UpdateVirtualConfiguration=8]=\"UpdateVirtualConfiguration\",l[l.SetInputElement=9]=\"SetInputElement\",l[l.SetButtonElement=10]=\"SetButtonElement\",l[l.SetOptionsElement=11]=\"SetOptionsElement\",l))(_||{});function T(t,i=e=>e){let e=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,n=i(t.options.slice()),o=n.length>0&&n[0].dataRef.current.order!==null?n.sort((u,a)=>u.dataRef.current.order-a.dataRef.current.order):E(n,u=>u.dataRef.current.domRef.current),r=e?o.indexOf(e):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}let D={[1](t){var i;return(i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](t){var i,e;if((i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===0)return t;if((e=t.dataRef.current)!=null&&e.value){let n=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(n!==-1)return{...t,activeOptionIndex:n,comboboxState:0,__demoMode:!1}}return{...t,comboboxState:0,__demoMode:!1}},[3](t,i){return t.isTyping===i.isTyping?t:{...t,isTyping:i.isTyping}},[2](t,i){var r,u,a,d;if((r=t.dataRef.current)!=null&&r.disabled||t.optionsElement&&!((u=t.dataRef.current)!=null&&u.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let{options:p,disabled:s}=t.virtual,b=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>p,resolveActiveIndex:()=>{var v,m;return(m=(v=t.activeOptionIndex)!=null?v:p.findIndex(y=>!s(y)))!=null?m:null},resolveDisabled:s,resolveId(){throw new Error(\"Function not implemented.\")}}),l=(a=i.trigger)!=null?a:2;return t.activeOptionIndex===b&&t.activationTrigger===l?t:{...t,activeOptionIndex:b,activationTrigger:l,isTyping:!1,__demoMode:!1}}let e=T(t);if(e.activeOptionIndex===null){let p=e.options.findIndex(s=>!s.dataRef.current.disabled);p!==-1&&(e.activeOptionIndex=p)}let n=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled}),o=(d=i.trigger)!=null?d:2;return t.activeOptionIndex===n&&t.activationTrigger===o?t:{...t,...e,isTyping:!1,activeOptionIndex:n,activationTrigger:o,__demoMode:!1}},[4]:(t,i)=>{var r,u,a,d;if((r=t.dataRef.current)!=null&&r.virtual)return{...t,options:[...t.options,i.payload]};let e=i.payload,n=T(t,p=>(p.push(e),p));t.activeOptionIndex===null&&(a=(u=t.dataRef.current).isSelected)!=null&&a.call(u,i.payload.dataRef.current.value)&&(n.activeOptionIndex=n.options.indexOf(e));let o={...t,...n,activationTrigger:2};return(d=t.dataRef.current)!=null&&d.__demoMode&&t.dataRef.current.value===void 0&&(o.activeOptionIndex=0),o},[5]:(t,i)=>{var n;if((n=t.dataRef.current)!=null&&n.virtual)return{...t,options:t.options.filter(o=>o.id!==i.id)};let e=T(t,o=>{let r=o.findIndex(u=>u.id===i.id);return r!==-1&&o.splice(r,1),o});return{...t,...e,activationTrigger:2}},[6]:(t,i)=>t.defaultToFirstOption===i.value?t:{...t,defaultToFirstOption:i.value},[7]:(t,i)=>t.activationTrigger===i.trigger?t:{...t,activationTrigger:i.trigger},[8]:(t,i)=>{var n,o;if(t.virtual===null)return{...t,virtual:{options:i.options,disabled:(n=i.disabled)!=null?n:()=>!1}};if(t.virtual.options===i.options&&t.virtual.disabled===i.disabled)return t;let e=t.activeOptionIndex;if(t.activeOptionIndex!==null){let r=i.options.indexOf(t.virtual.options[t.activeOptionIndex]);r!==-1?e=r:e=null}return{...t,activeOptionIndex:e,virtual:{options:i.options,disabled:(o=i.disabled)!=null?o:()=>!1}}},[9]:(t,i)=>t.inputElement===i.element?t:{...t,inputElement:i.element},[10]:(t,i)=>t.buttonElement===i.element?t:{...t,buttonElement:i.element},[11]:(t,i)=>t.optionsElement===i.element?t:{...t,optionsElement:i.element}};class O extends h{constructor(e){super(e);c(this,\"actions\",{onChange:e=>{let{onChange:n,compare:o,mode:r,value:u}=this.state.dataRef.current;return g(r,{[0]:()=>n==null?void 0:n(e),[1]:()=>{let a=u.slice(),d=a.findIndex(p=>o(p,e));return d===-1?a.push(e):a.splice(d,1),n==null?void 0:n(a)}})},registerOption:(e,n)=>(this.send({type:4,payload:{id:e,dataRef:n}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(n.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:e})}),goToOption:(e,n)=>(this.send({type:6,value:!1}),this.send({type:2,...e,trigger:n})),setIsTyping:e=>{this.send({type:3,isTyping:e})},closeCombobox:()=>{var e,n;this.send({type:1}),this.send({type:6,value:!1}),(n=(e=this.state.dataRef.current).onClose)==null||n.call(e)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:e=>{this.send({type:7,trigger:e})},selectActiveOption:()=>{let e=this.selectors.activeOptionIndex(this.state);if(e!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[e]);else{let{dataRef:n}=this.state.options[e];this.actions.onChange(n.current.value)}this.actions.goToOption({focus:f.Specific,idx:e})}},setInputElement:e=>{this.send({type:9,element:e})},setButtonElement:e=>{this.send({type:10,element:e})},setOptionsElement:e=>{this.send({type:11,element:e})}});c(this,\"selectors\",{activeDescendantId:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);if(n!==null)return e.virtual?(r=e.options.find(u=>!u.dataRef.current.disabled&&e.dataRef.current.compare(u.dataRef.current.value,e.virtual.options[n])))==null?void 0:r.id:(o=e.options[n])==null?void 0:o.id},activeOptionIndex:e=>{if(e.defaultToFirstOption&&e.activeOptionIndex===null&&(e.virtual?e.virtual.options.length>0:e.options.length>0)){if(e.virtual){let{options:o,disabled:r}=e.virtual,u=o.findIndex(a=>{var d;return!((d=r==null?void 0:r(a))!=null&&d)});if(u!==-1)return u}let n=e.options.findIndex(o=>!o.dataRef.current.disabled);if(n!==-1)return n}return e.activeOptionIndex},activeOption:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);return n===null?null:e.virtual?e.virtual.options[n!=null?n:0]:(r=(o=e.options[n])==null?void 0:o.dataRef.current.value)!=null?r:null},isActive:(e,n,o)=>{var u;let r=this.selectors.activeOptionIndex(e);return r===null?!1:e.virtual?r===e.dataRef.current.calculateIndex(n):((u=e.options[r])==null?void 0:u.id)===o},shouldScrollIntoView:(e,n,o)=>!(e.virtual||e.__demoMode||e.comboboxState!==0||e.activationTrigger===0||!this.selectors.isActive(e,n,o))});{let n=this.state.id,o=A.get(null);this.disposables.add(o.on(R.Push,r=>{!o.selectors.isTop(r,n)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>o.actions.push(n)),this.on(1,()=>o.actions.pop(n))}}static new({id:e,virtual:n=null,__demoMode:o=!1}){var r;return new O({id:e,dataRef:{current:{}},comboboxState:o?0:1,isTyping:!1,options:[],virtual:n?{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:o})}reduce(e,n){return g(n.type,D,e,n)}}export{_ as ActionTypes,F as ActivationTrigger,O as ComboboxMachine,C as ComboboxState,M as ValueMode};\n"], "mappings": "AAAA,IAAIA,CAAC,GAACC,MAAM,CAACC,cAAc;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,IAAID,CAAC,GAACJ,CAAC,CAACI,CAAC,EAACC,CAAC,EAAC;EAACE,UAAU,EAAC,CAAC,CAAC;EAACC,YAAY,EAAC,CAAC,CAAC;EAACC,QAAQ,EAAC,CAAC,CAAC;EAACC,KAAK,EAACJ;AAAC,CAAC,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC;AAAC,IAAIK,CAAC,GAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIH,CAAC,CAACC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,EAAE,GAACA,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,SAAOM,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,EAACC,oBAAoB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,IAAIC,CAAC,GAAC,CAACpB,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACqB,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACrB,CAAC,CAACA,CAAC,CAACsB,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACtB,CAAC,CAAC,EAAEoB,CAAC,IAAE,CAAC,CAAC,CAAC;EAACG,CAAC,GAAC,CAACvB,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACwB,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACxB,CAAC,CAACA,CAAC,CAACyB,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACzB,CAAC,CAAC,EAAEuB,CAAC,IAAE,CAAC,CAAC,CAAC;EAACG,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACD,CAAC,CAACA,CAAC,CAACf,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACe,CAAC,CAACA,CAAC,CAACE,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;EAACI,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,YAAY,GAAC,CAAC,CAAC,GAAC,cAAc,EAACD,CAAC,CAACA,CAAC,CAACE,aAAa,GAAC,CAAC,CAAC,GAAC,eAAe,EAACF,CAAC,CAACA,CAAC,CAACG,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACH,CAAC,CAACA,CAAC,CAACI,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACJ,CAAC,CAACA,CAAC,CAACK,cAAc,GAAC,CAAC,CAAC,GAAC,gBAAgB,EAACL,CAAC,CAACA,CAAC,CAACM,gBAAgB,GAAC,CAAC,CAAC,GAAC,kBAAkB,EAACN,CAAC,CAACA,CAAC,CAACO,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACP,CAAC,CAACA,CAAC,CAACQ,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACR,CAAC,CAACA,CAAC,CAACS,0BAA0B,GAAC,CAAC,CAAC,GAAC,4BAA4B,EAACT,CAAC,CAACA,CAAC,CAACU,eAAe,GAAC,CAAC,CAAC,GAAC,iBAAiB,EAACV,CAAC,CAACA,CAAC,CAACW,gBAAgB,GAAC,EAAE,CAAC,GAAC,kBAAkB,EAACX,CAAC,CAACA,CAAC,CAACY,iBAAiB,GAAC,EAAE,CAAC,GAAC,mBAAmB,EAACZ,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASc,CAACA,CAAC9C,CAAC,EAACC,CAAC,GAACC,CAAC,IAAEA,CAAC,EAAC;EAAC,IAAIA,CAAC,GAACF,CAAC,CAAC+C,iBAAiB,KAAG,IAAI,GAAC/C,CAAC,CAACgD,OAAO,CAAChD,CAAC,CAAC+C,iBAAiB,CAAC,GAAC,IAAI;IAAClB,CAAC,GAAC5B,CAAC,CAACD,CAAC,CAACgD,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;IAACC,CAAC,GAACrB,CAAC,CAACsB,MAAM,GAAC,CAAC,IAAEtB,CAAC,CAAC,CAAC,CAAC,CAACuB,OAAO,CAACC,OAAO,CAACC,KAAK,KAAG,IAAI,GAACzB,CAAC,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACC,KAAK,GAACG,CAAC,CAACL,OAAO,CAACC,OAAO,CAACC,KAAK,CAAC,GAACnC,CAAC,CAACU,CAAC,EAAC2B,CAAC,IAAEA,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACK,MAAM,CAACL,OAAO,CAAC;IAACM,CAAC,GAACzD,CAAC,GAACgD,CAAC,CAACU,OAAO,CAAC1D,CAAC,CAAC,GAAC,IAAI;EAAC,OAAOyD,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC;IAACX,OAAO,EAACE,CAAC;IAACH,iBAAiB,EAACY;EAAC,CAAC;AAAA;AAAC,IAAIE,CAAC,GAAC;EAAC,CAAC,CAAC,EAAE7D,CAAC,EAAC;IAAC,IAAIC,CAAC;IAAC,OAAM,CAACA,CAAC,GAACD,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEpD,CAAC,CAAC6D,QAAQ,IAAE9D,CAAC,CAAC+D,aAAa,KAAG,CAAC,GAAC/D,CAAC,GAAC;MAAC,GAAGA,CAAC;MAAC+C,iBAAiB,EAAC,IAAI;MAACgB,aAAa,EAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAACC,iBAAiB,EAAC,CAAC;MAACC,UAAU,EAAC,CAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAElE,CAAC,EAAC;IAAC,IAAIC,CAAC,EAACC,CAAC;IAAC,IAAG,CAACD,CAAC,GAACD,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEpD,CAAC,CAAC6D,QAAQ,IAAE9D,CAAC,CAAC+D,aAAa,KAAG,CAAC,EAAC,OAAO/D,CAAC;IAAC,IAAG,CAACE,CAAC,GAACF,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEnD,CAAC,CAACI,KAAK,EAAC;MAAC,IAAIuB,CAAC,GAAC7B,CAAC,CAACoD,OAAO,CAACC,OAAO,CAACc,cAAc,CAACnE,CAAC,CAACoD,OAAO,CAACC,OAAO,CAAC/C,KAAK,CAAC;MAAC,IAAGuB,CAAC,KAAG,CAAC,CAAC,EAAC,OAAM;QAAC,GAAG7B,CAAC;QAAC+C,iBAAiB,EAAClB,CAAC;QAACkC,aAAa,EAAC,CAAC;QAACG,UAAU,EAAC,CAAC;MAAC,CAAC;IAAA;IAAC,OAAM;MAAC,GAAGlE,CAAC;MAAC+D,aAAa,EAAC,CAAC;MAACG,UAAU,EAAC,CAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAElE,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAACgE,QAAQ,KAAG/D,CAAC,CAAC+D,QAAQ,GAAChE,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACgE,QAAQ,EAAC/D,CAAC,CAAC+D;IAAQ,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAEhE,CAAC,EAACC,CAAC,EAAC;IAAC,IAAI0D,CAAC,EAACH,CAAC,EAACC,CAAC,EAACW,CAAC;IAAC,IAAG,CAACT,CAAC,GAAC3D,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACG,QAAQ,IAAE9D,CAAC,CAACqE,cAAc,IAAE,EAAE,CAACb,CAAC,GAACxD,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEG,CAAC,CAACc,eAAe,CAACjB,OAAO,CAACkB,MAAM,CAAC,IAAEvE,CAAC,CAAC+D,aAAa,KAAG,CAAC,EAAC,OAAO/D,CAAC;IAAC,IAAGA,CAAC,CAACwE,OAAO,EAAC;MAAC,IAAG;UAACxB,OAAO,EAACyB,CAAC;UAACX,QAAQ,EAACY;QAAC,CAAC,GAAC1E,CAAC,CAACwE,OAAO;QAACG,CAAC,GAAC1E,CAAC,CAAC2E,KAAK,KAAG7D,CAAC,CAAC8D,QAAQ,GAAC5E,CAAC,CAAC6E,GAAG,GAAC7D,CAAC,CAAChB,CAAC,EAAC;UAAC8E,YAAY,EAACA,CAAA,KAAIN,CAAC;UAACO,kBAAkB,EAACA,CAAA,KAAI;YAAC,IAAIC,CAAC,EAACC,CAAC;YAAC,OAAM,CAACA,CAAC,GAAC,CAACD,CAAC,GAACjF,CAAC,CAAC+C,iBAAiB,KAAG,IAAI,GAACkC,CAAC,GAACR,CAAC,CAACU,SAAS,CAACC,CAAC,IAAE,CAACV,CAAC,CAACU,CAAC,CAAC,CAAC,KAAG,IAAI,GAACF,CAAC,GAAC,IAAI;UAAA,CAAC;UAACG,eAAe,EAACX,CAAC;UAACY,SAASA,CAAA,EAAE;YAAC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;UAAA;QAAC,CAAC,CAAC;QAACtD,CAAC,GAAC,CAACwB,CAAC,GAACxD,CAAC,CAACuF,OAAO,KAAG,IAAI,GAAC/B,CAAC,GAAC,CAAC;MAAC,OAAOzD,CAAC,CAAC+C,iBAAiB,KAAG4B,CAAC,IAAE3E,CAAC,CAACiE,iBAAiB,KAAGhC,CAAC,GAACjC,CAAC,GAAC;QAAC,GAAGA,CAAC;QAAC+C,iBAAiB,EAAC4B,CAAC;QAACV,iBAAiB,EAAChC,CAAC;QAAC+B,QAAQ,EAAC,CAAC,CAAC;QAACE,UAAU,EAAC,CAAC;MAAC,CAAC;IAAA;IAAC,IAAIhE,CAAC,GAAC4C,CAAC,CAAC9C,CAAC,CAAC;IAAC,IAAGE,CAAC,CAAC6C,iBAAiB,KAAG,IAAI,EAAC;MAAC,IAAI0B,CAAC,GAACvE,CAAC,CAAC8C,OAAO,CAACmC,SAAS,CAACT,CAAC,IAAE,CAACA,CAAC,CAACtB,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;MAACW,CAAC,KAAG,CAAC,CAAC,KAAGvE,CAAC,CAAC6C,iBAAiB,GAAC0B,CAAC,CAAC;IAAA;IAAC,IAAI5C,CAAC,GAAC5B,CAAC,CAAC2E,KAAK,KAAG7D,CAAC,CAAC8D,QAAQ,GAAC5E,CAAC,CAAC6E,GAAG,GAAC7D,CAAC,CAAChB,CAAC,EAAC;QAAC8E,YAAY,EAACA,CAAA,KAAI7E,CAAC,CAAC8C,OAAO;QAACgC,kBAAkB,EAACA,CAAA,KAAI9E,CAAC,CAAC6C,iBAAiB;QAACuC,SAAS,EAACb,CAAC,IAAEA,CAAC,CAACgB,EAAE;QAACJ,eAAe,EAACZ,CAAC,IAAEA,CAAC,CAACrB,OAAO,CAACC,OAAO,CAACS;MAAQ,CAAC,CAAC;MAACZ,CAAC,GAAC,CAACkB,CAAC,GAACnE,CAAC,CAACuF,OAAO,KAAG,IAAI,GAACpB,CAAC,GAAC,CAAC;IAAC,OAAOpE,CAAC,CAAC+C,iBAAiB,KAAGlB,CAAC,IAAE7B,CAAC,CAACiE,iBAAiB,KAAGf,CAAC,GAAClD,CAAC,GAAC;MAAC,GAAGA,CAAC;MAAC,GAAGE,CAAC;MAAC8D,QAAQ,EAAC,CAAC,CAAC;MAACjB,iBAAiB,EAAClB,CAAC;MAACoC,iBAAiB,EAACf,CAAC;MAACgB,UAAU,EAAC,CAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAAClE,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI0D,CAAC,EAACH,CAAC,EAACC,CAAC,EAACW,CAAC;IAAC,IAAG,CAACT,CAAC,GAAC3D,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACa,OAAO,EAAC,OAAM;MAAC,GAAGxE,CAAC;MAACgD,OAAO,EAAC,CAAC,GAAGhD,CAAC,CAACgD,OAAO,EAAC/C,CAAC,CAACyF,OAAO;IAAC,CAAC;IAAC,IAAIxF,CAAC,GAACD,CAAC,CAACyF,OAAO;MAAC7D,CAAC,GAACiB,CAAC,CAAC9C,CAAC,EAACyE,CAAC,KAAGA,CAAC,CAACkB,IAAI,CAACzF,CAAC,CAAC,EAACuE,CAAC,CAAC,CAAC;IAACzE,CAAC,CAAC+C,iBAAiB,KAAG,IAAI,IAAE,CAACU,CAAC,GAAC,CAACD,CAAC,GAACxD,CAAC,CAACoD,OAAO,CAACC,OAAO,EAAEuC,UAAU,KAAG,IAAI,IAAEnC,CAAC,CAACoC,IAAI,CAACrC,CAAC,EAACvD,CAAC,CAACyF,OAAO,CAACtC,OAAO,CAACC,OAAO,CAAC/C,KAAK,CAAC,KAAGuB,CAAC,CAACkB,iBAAiB,GAAClB,CAAC,CAACmB,OAAO,CAACY,OAAO,CAAC1D,CAAC,CAAC,CAAC;IAAC,IAAIgD,CAAC,GAAC;MAAC,GAAGlD,CAAC;MAAC,GAAG6B,CAAC;MAACoC,iBAAiB,EAAC;IAAC,CAAC;IAAC,OAAM,CAACG,CAAC,GAACpE,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEe,CAAC,CAACF,UAAU,IAAElE,CAAC,CAACoD,OAAO,CAACC,OAAO,CAAC/C,KAAK,KAAG,KAAK,CAAC,KAAG4C,CAAC,CAACH,iBAAiB,GAAC,CAAC,CAAC,EAACG,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAAClD,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI4B,CAAC;IAAC,IAAG,CAACA,CAAC,GAAC7B,CAAC,CAACoD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAExB,CAAC,CAAC2C,OAAO,EAAC,OAAM;MAAC,GAAGxE,CAAC;MAACgD,OAAO,EAAChD,CAAC,CAACgD,OAAO,CAAC8C,MAAM,CAAC5C,CAAC,IAAEA,CAAC,CAACuC,EAAE,KAAGxF,CAAC,CAACwF,EAAE;IAAC,CAAC;IAAC,IAAIvF,CAAC,GAAC4C,CAAC,CAAC9C,CAAC,EAACkD,CAAC,IAAE;MAAC,IAAIS,CAAC,GAACT,CAAC,CAACiC,SAAS,CAAC3B,CAAC,IAAEA,CAAC,CAACiC,EAAE,KAAGxF,CAAC,CAACwF,EAAE,CAAC;MAAC,OAAO9B,CAAC,KAAG,CAAC,CAAC,IAAET,CAAC,CAAC6C,MAAM,CAACpC,CAAC,EAAC,CAAC,CAAC,EAACT,CAAC;IAAA,CAAC,CAAC;IAAC,OAAM;MAAC,GAAGlD,CAAC;MAAC,GAAGE,CAAC;MAAC+D,iBAAiB,EAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAACjE,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACgG,oBAAoB,KAAG/F,CAAC,CAACK,KAAK,GAACN,CAAC,GAAC;IAAC,GAAGA,CAAC;IAACgG,oBAAoB,EAAC/F,CAAC,CAACK;EAAK,CAAC;EAAC,CAAC,CAAC,GAAE,CAACN,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACiE,iBAAiB,KAAGhE,CAAC,CAACuF,OAAO,GAACxF,CAAC,GAAC;IAAC,GAAGA,CAAC;IAACiE,iBAAiB,EAAChE,CAAC,CAACuF;EAAO,CAAC;EAAC,CAAC,CAAC,GAAE,CAACxF,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI4B,CAAC,EAACqB,CAAC;IAAC,IAAGlD,CAAC,CAACwE,OAAO,KAAG,IAAI,EAAC,OAAM;MAAC,GAAGxE,CAAC;MAACwE,OAAO,EAAC;QAACxB,OAAO,EAAC/C,CAAC,CAAC+C,OAAO;QAACc,QAAQ,EAAC,CAACjC,CAAC,GAAC5B,CAAC,CAAC6D,QAAQ,KAAG,IAAI,GAACjC,CAAC,GAAC,MAAI,CAAC;MAAC;IAAC,CAAC;IAAC,IAAG7B,CAAC,CAACwE,OAAO,CAACxB,OAAO,KAAG/C,CAAC,CAAC+C,OAAO,IAAEhD,CAAC,CAACwE,OAAO,CAACV,QAAQ,KAAG7D,CAAC,CAAC6D,QAAQ,EAAC,OAAO9D,CAAC;IAAC,IAAIE,CAAC,GAACF,CAAC,CAAC+C,iBAAiB;IAAC,IAAG/C,CAAC,CAAC+C,iBAAiB,KAAG,IAAI,EAAC;MAAC,IAAIY,CAAC,GAAC1D,CAAC,CAAC+C,OAAO,CAACY,OAAO,CAAC5D,CAAC,CAACwE,OAAO,CAACxB,OAAO,CAAChD,CAAC,CAAC+C,iBAAiB,CAAC,CAAC;MAACY,CAAC,KAAG,CAAC,CAAC,GAACzD,CAAC,GAACyD,CAAC,GAACzD,CAAC,GAAC,IAAI;IAAA;IAAC,OAAM;MAAC,GAAGF,CAAC;MAAC+C,iBAAiB,EAAC7C,CAAC;MAACsE,OAAO,EAAC;QAACxB,OAAO,EAAC/C,CAAC,CAAC+C,OAAO;QAACc,QAAQ,EAAC,CAACZ,CAAC,GAACjD,CAAC,CAAC6D,QAAQ,KAAG,IAAI,GAACZ,CAAC,GAAC,MAAI,CAAC;MAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAAClD,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACiG,YAAY,KAAGhG,CAAC,CAACiG,OAAO,GAAClG,CAAC,GAAC;IAAC,GAAGA,CAAC;IAACiG,YAAY,EAAChG,CAAC,CAACiG;EAAO,CAAC;EAAC,CAAC,EAAE,GAAE,CAAClG,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACmG,aAAa,KAAGlG,CAAC,CAACiG,OAAO,GAAClG,CAAC,GAAC;IAAC,GAAGA,CAAC;IAACmG,aAAa,EAAClG,CAAC,CAACiG;EAAO,CAAC;EAAC,CAAC,EAAE,GAAE,CAAClG,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACqE,cAAc,KAAGpE,CAAC,CAACiG,OAAO,GAAClG,CAAC,GAAC;IAAC,GAAGA,CAAC;IAACqE,cAAc,EAACpE,CAAC,CAACiG;EAAO;AAAC,CAAC;AAAC,MAAME,CAAC,SAAS3F,CAAC;EAAC4F,WAAWA,CAACnG,CAAC,EAAC;IAAC,KAAK,CAACA,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,SAAS,EAAC;MAAC+F,QAAQ,EAACpG,CAAC,IAAE;QAAC,IAAG;UAACoG,QAAQ,EAACzE,CAAC;UAAC0E,OAAO,EAACrD,CAAC;UAACsD,IAAI,EAAC7C,CAAC;UAACrD,KAAK,EAACkD;QAAC,CAAC,GAAC,IAAI,CAACiD,KAAK,CAACrD,OAAO,CAACC,OAAO;QAAC,OAAOhC,CAAC,CAACsC,CAAC,EAAC;UAAC,CAAC,CAAC,GAAE,MAAI9B,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC3B,CAAC,CAAC;UAAC,CAAC,CAAC,GAAE,MAAI;YAAC,IAAIuD,CAAC,GAACD,CAAC,CAACP,KAAK,CAAC,CAAC;cAACmB,CAAC,GAACX,CAAC,CAAC0B,SAAS,CAACV,CAAC,IAAEvB,CAAC,CAACuB,CAAC,EAACvE,CAAC,CAAC,CAAC;YAAC,OAAOkE,CAAC,KAAG,CAAC,CAAC,GAACX,CAAC,CAACkC,IAAI,CAACzF,CAAC,CAAC,GAACuD,CAAC,CAACsC,MAAM,CAAC3B,CAAC,EAAC,CAAC,CAAC,EAACvC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC4B,CAAC,CAAC;UAAA;QAAC,CAAC,CAAC;MAAA,CAAC;MAACiD,cAAc,EAACA,CAACxG,CAAC,EAAC2B,CAAC,MAAI,IAAI,CAAC8E,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAAClB,OAAO,EAAC;UAACD,EAAE,EAACvF,CAAC;UAACkD,OAAO,EAACvB;QAAC;MAAC,CAAC,CAAC,EAAC,MAAI;QAAC,IAAI,CAAC4E,KAAK,CAAC1D,iBAAiB,KAAG,IAAI,CAAC0D,KAAK,CAACrD,OAAO,CAACC,OAAO,CAACc,cAAc,CAACtC,CAAC,CAACwB,OAAO,CAAC/C,KAAK,CAAC,IAAE,IAAI,CAACqG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACtG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACqG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACnB,EAAE,EAACvF;QAAC,CAAC,CAAC;MAAA,CAAC,CAAC;MAAC2G,UAAU,EAACA,CAAC3G,CAAC,EAAC2B,CAAC,MAAI,IAAI,CAAC8E,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACtG,KAAK,EAAC,CAAC;MAAC,CAAC,CAAC,EAAC,IAAI,CAACqG,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAAC,GAAG1G,CAAC;QAACsF,OAAO,EAAC3D;MAAC,CAAC,CAAC,CAAC;MAACiF,WAAW,EAAC5G,CAAC,IAAE;QAAC,IAAI,CAACyG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAAC5C,QAAQ,EAAC9D;QAAC,CAAC,CAAC;MAAA,CAAC;MAAC6G,aAAa,EAACA,CAAA,KAAI;QAAC,IAAI7G,CAAC,EAAC2B,CAAC;QAAC,IAAI,CAAC8E,IAAI,CAAC;UAACC,IAAI,EAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACD,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACtG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC,CAACuB,CAAC,GAAC,CAAC3B,CAAC,GAAC,IAAI,CAACuG,KAAK,CAACrD,OAAO,CAACC,OAAO,EAAE2D,OAAO,KAAG,IAAI,IAAEnF,CAAC,CAACgE,IAAI,CAAC3F,CAAC,CAAC;MAAA,CAAC;MAAC+G,YAAY,EAACA,CAAA,KAAI;QAAC,IAAI,CAACN,IAAI,CAAC;UAACC,IAAI,EAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACD,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACtG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC;MAAA,CAAC;MAAC4G,oBAAoB,EAAChH,CAAC,IAAE;QAAC,IAAI,CAACyG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACpB,OAAO,EAACtF;QAAC,CAAC,CAAC;MAAA,CAAC;MAACiH,kBAAkB,EAACA,CAAA,KAAI;QAAC,IAAIjH,CAAC,GAAC,IAAI,CAACkH,SAAS,CAACrE,iBAAiB,CAAC,IAAI,CAAC0D,KAAK,CAAC;QAAC,IAAGvG,CAAC,KAAG,IAAI,EAAC;UAAC,IAAG,IAAI,CAACmH,OAAO,CAACP,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACL,KAAK,CAACjC,OAAO,EAAC,IAAI,CAAC6C,OAAO,CAACf,QAAQ,CAAC,IAAI,CAACG,KAAK,CAACjC,OAAO,CAACxB,OAAO,CAAC9C,CAAC,CAAC,CAAC,CAAC,KAAI;YAAC,IAAG;cAACkD,OAAO,EAACvB;YAAC,CAAC,GAAC,IAAI,CAAC4E,KAAK,CAACzD,OAAO,CAAC9C,CAAC,CAAC;YAAC,IAAI,CAACmH,OAAO,CAACf,QAAQ,CAACzE,CAAC,CAACwB,OAAO,CAAC/C,KAAK,CAAC;UAAA;UAAC,IAAI,CAAC+G,OAAO,CAACR,UAAU,CAAC;YAACjC,KAAK,EAAC7D,CAAC,CAAC8D,QAAQ;YAACC,GAAG,EAAC5E;UAAC,CAAC,CAAC;QAAA;MAAC,CAAC;MAACoH,eAAe,EAACpH,CAAC,IAAE;QAAC,IAAI,CAACyG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACV,OAAO,EAAChG;QAAC,CAAC,CAAC;MAAA,CAAC;MAACqH,gBAAgB,EAACrH,CAAC,IAAE;QAAC,IAAI,CAACyG,IAAI,CAAC;UAACC,IAAI,EAAC,EAAE;UAACV,OAAO,EAAChG;QAAC,CAAC,CAAC;MAAA,CAAC;MAACsH,iBAAiB,EAACtH,CAAC,IAAE;QAAC,IAAI,CAACyG,IAAI,CAAC;UAACC,IAAI,EAAC,EAAE;UAACV,OAAO,EAAChG;QAAC,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,WAAW,EAAC;MAACkH,kBAAkB,EAACvH,CAAC,IAAE;QAAC,IAAIgD,CAAC,EAACS,CAAC;QAAC,IAAI9B,CAAC,GAAC,IAAI,CAACuF,SAAS,CAACrE,iBAAiB,CAAC7C,CAAC,CAAC;QAAC,IAAG2B,CAAC,KAAG,IAAI,EAAC,OAAO3B,CAAC,CAACsE,OAAO,GAAC,CAACb,CAAC,GAACzD,CAAC,CAAC8C,OAAO,CAAC0E,IAAI,CAAClE,CAAC,IAAE,CAACA,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACS,QAAQ,IAAE5D,CAAC,CAACkD,OAAO,CAACC,OAAO,CAACkD,OAAO,CAAC/C,CAAC,CAACJ,OAAO,CAACC,OAAO,CAAC/C,KAAK,EAACJ,CAAC,CAACsE,OAAO,CAACxB,OAAO,CAACnB,CAAC,CAAC,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC8B,CAAC,CAAC8B,EAAE,GAAC,CAACvC,CAAC,GAAChD,CAAC,CAAC8C,OAAO,CAACnB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACqB,CAAC,CAACuC,EAAE;MAAA,CAAC;MAAC1C,iBAAiB,EAAC7C,CAAC,IAAE;QAAC,IAAGA,CAAC,CAAC8F,oBAAoB,IAAE9F,CAAC,CAAC6C,iBAAiB,KAAG,IAAI,KAAG7C,CAAC,CAACsE,OAAO,GAACtE,CAAC,CAACsE,OAAO,CAACxB,OAAO,CAACG,MAAM,GAAC,CAAC,GAACjD,CAAC,CAAC8C,OAAO,CAACG,MAAM,GAAC,CAAC,CAAC,EAAC;UAAC,IAAGjD,CAAC,CAACsE,OAAO,EAAC;YAAC,IAAG;gBAACxB,OAAO,EAACE,CAAC;gBAACY,QAAQ,EAACH;cAAC,CAAC,GAACzD,CAAC,CAACsE,OAAO;cAAChB,CAAC,GAACN,CAAC,CAACiC,SAAS,CAAC1B,CAAC,IAAE;gBAAC,IAAIW,CAAC;gBAAC,OAAM,EAAE,CAACA,CAAC,GAACT,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACF,CAAC,CAAC,KAAG,IAAI,IAAEW,CAAC,CAAC;cAAA,CAAC,CAAC;YAAC,IAAGZ,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;UAAA;UAAC,IAAI3B,CAAC,GAAC3B,CAAC,CAAC8C,OAAO,CAACmC,SAAS,CAACjC,CAAC,IAAE,CAACA,CAAC,CAACE,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;UAAC,IAAGjC,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;QAAA;QAAC,OAAO3B,CAAC,CAAC6C,iBAAiB;MAAA,CAAC;MAAC4E,YAAY,EAACzH,CAAC,IAAE;QAAC,IAAIgD,CAAC,EAACS,CAAC;QAAC,IAAI9B,CAAC,GAAC,IAAI,CAACuF,SAAS,CAACrE,iBAAiB,CAAC7C,CAAC,CAAC;QAAC,OAAO2B,CAAC,KAAG,IAAI,GAAC,IAAI,GAAC3B,CAAC,CAACsE,OAAO,GAACtE,CAAC,CAACsE,OAAO,CAACxB,OAAO,CAACnB,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC8B,CAAC,GAAC,CAACT,CAAC,GAAChD,CAAC,CAAC8C,OAAO,CAACnB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACqB,CAAC,CAACE,OAAO,CAACC,OAAO,CAAC/C,KAAK,KAAG,IAAI,GAACqD,CAAC,GAAC,IAAI;MAAA,CAAC;MAACiE,QAAQ,EAACA,CAAC1H,CAAC,EAAC2B,CAAC,EAACqB,CAAC,KAAG;QAAC,IAAIM,CAAC;QAAC,IAAIG,CAAC,GAAC,IAAI,CAACyD,SAAS,CAACrE,iBAAiB,CAAC7C,CAAC,CAAC;QAAC,OAAOyD,CAAC,KAAG,IAAI,GAAC,CAAC,CAAC,GAACzD,CAAC,CAACsE,OAAO,GAACb,CAAC,KAAGzD,CAAC,CAACkD,OAAO,CAACC,OAAO,CAACc,cAAc,CAACtC,CAAC,CAAC,GAAC,CAAC,CAAC2B,CAAC,GAACtD,CAAC,CAAC8C,OAAO,CAACW,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACH,CAAC,CAACiC,EAAE,MAAIvC,CAAC;MAAA,CAAC;MAAC2E,oBAAoB,EAACA,CAAC3H,CAAC,EAAC2B,CAAC,EAACqB,CAAC,KAAG,EAAEhD,CAAC,CAACsE,OAAO,IAAEtE,CAAC,CAACgE,UAAU,IAAEhE,CAAC,CAAC6D,aAAa,KAAG,CAAC,IAAE7D,CAAC,CAAC+D,iBAAiB,KAAG,CAAC,IAAE,CAAC,IAAI,CAACmD,SAAS,CAACQ,QAAQ,CAAC1H,CAAC,EAAC2B,CAAC,EAACqB,CAAC,CAAC;IAAC,CAAC,CAAC;IAAC;MAAC,IAAIrB,CAAC,GAAC,IAAI,CAAC4E,KAAK,CAAChB,EAAE;QAACvC,CAAC,GAACrC,CAAC,CAACiH,GAAG,CAAC,IAAI,CAAC;MAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC9E,CAAC,CAAC+E,EAAE,CAACtH,CAAC,CAACuH,IAAI,EAACvE,CAAC,IAAE;QAAC,CAACT,CAAC,CAACkE,SAAS,CAACe,KAAK,CAACxE,CAAC,EAAC9B,CAAC,CAAC,IAAE,IAAI,CAAC4E,KAAK,CAAC1C,aAAa,KAAG,CAAC,IAAE,IAAI,CAACsD,OAAO,CAACN,aAAa,CAAC,CAAC;MAAA,CAAC,CAAC,CAAC,EAAC,IAAI,CAACkB,EAAE,CAAC,CAAC,EAAC,MAAI/E,CAAC,CAACmE,OAAO,CAAC1B,IAAI,CAAC9D,CAAC,CAAC,CAAC,EAAC,IAAI,CAACoG,EAAE,CAAC,CAAC,EAAC,MAAI/E,CAAC,CAACmE,OAAO,CAACe,GAAG,CAACvG,CAAC,CAAC,CAAC;IAAA;EAAC;EAAC,OAAOwG,GAAGA,CAAC;IAAC5C,EAAE,EAACvF,CAAC;IAACsE,OAAO,EAAC3C,CAAC,GAAC,IAAI;IAACqC,UAAU,EAAChB,CAAC,GAAC,CAAC;EAAC,CAAC,EAAC;IAAC,IAAIS,CAAC;IAAC,OAAO,IAAIyC,CAAC,CAAC;MAACX,EAAE,EAACvF,CAAC;MAACkD,OAAO,EAAC;QAACC,OAAO,EAAC,CAAC;MAAC,CAAC;MAACU,aAAa,EAACb,CAAC,GAAC,CAAC,GAAC,CAAC;MAACc,QAAQ,EAAC,CAAC,CAAC;MAAChB,OAAO,EAAC,EAAE;MAACwB,OAAO,EAAC3C,CAAC,GAAC;QAACmB,OAAO,EAACnB,CAAC,CAACmB,OAAO;QAACc,QAAQ,EAAC,CAACH,CAAC,GAAC9B,CAAC,CAACiC,QAAQ,KAAG,IAAI,GAACH,CAAC,GAAC,MAAI,CAAC;MAAC,CAAC,GAAC,IAAI;MAACZ,iBAAiB,EAAC,IAAI;MAACkB,iBAAiB,EAAC,CAAC;MAACgC,YAAY,EAAC,IAAI;MAACE,aAAa,EAAC,IAAI;MAAC9B,cAAc,EAAC,IAAI;MAACH,UAAU,EAAChB;IAAC,CAAC,CAAC;EAAA;EAACoF,MAAMA,CAACpI,CAAC,EAAC2B,CAAC,EAAC;IAAC,OAAOR,CAAC,CAACQ,CAAC,CAAC+E,IAAI,EAAC/C,CAAC,EAAC3D,CAAC,EAAC2B,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOG,CAAC,IAAItB,WAAW,EAACkB,CAAC,IAAI2G,iBAAiB,EAACnC,CAAC,IAAIoC,eAAe,EAAClH,CAAC,IAAImH,aAAa,EAAChH,CAAC,IAAIiH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}