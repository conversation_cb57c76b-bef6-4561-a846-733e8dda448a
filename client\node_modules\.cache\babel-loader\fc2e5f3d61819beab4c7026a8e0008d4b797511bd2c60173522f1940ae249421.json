{"ast": null, "code": "import { createEventHandler as $93925083ecbb358c$export$48d1ea6320830260 } from \"./createEventHandler.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $46d819fcbaf35654$export$8f71654801c2f7cd(props) {\n  return {\n    keyboardProps: props.isDisabled ? {} : {\n      onKeyDown: (0, $93925083ecbb358c$export$48d1ea6320830260)(props.onKeyDown),\n      onKeyUp: (0, $93925083ecbb358c$export$48d1ea6320830260)(props.onKeyUp)\n    }\n  };\n}\nexport { $46d819fcbaf35654$export$8f71654801c2f7cd as useKeyboard };", "map": {"version": 3, "names": ["$46d819fcbaf35654$export$8f71654801c2f7cd", "props", "keyboardProps", "isDisabled", "onKeyDown", "$93925083ecbb358c$export$48d1ea6320830260", "onKeyUp"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useKeyboard.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {createEventHandler} from './createEventHandler';\nimport {DOMAttributes, KeyboardEvents} from '@react-types/shared';\n\nexport interface KeyboardProps extends KeyboardEvents {\n  /** Whether the keyboard events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface KeyboardResult {\n  /** Props to spread onto the target element. */\n  keyboardProps: DOMAttributes\n}\n\n/**\n * Handles keyboard interactions for a focusable element.\n */\nexport function useKeyboard(props: KeyboardProps): KeyboardResult {\n  return {\n    keyboardProps: props.isDisabled ? {} : {\n      onKeyDown: createEventHand<PERSON>(props.onKeyDown),\n      onKeyUp: createEvent<PERSON>and<PERSON>(props.onKeyUp)\n    }\n  };\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AA4BO,SAASA,0CAAYC,KAAoB;EAC9C,OAAO;IACLC,aAAA,EAAeD,KAAA,CAAME,UAAU,GAAG,CAAC,IAAI;MACrCC,SAAA,EAAW,IAAAC,yCAAiB,EAAEJ,KAAA,CAAMG,SAAS;MAC7CE,OAAA,EAAS,IAAAD,yCAAiB,EAAEJ,KAAA,CAAMK,OAAO;IAC3C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}