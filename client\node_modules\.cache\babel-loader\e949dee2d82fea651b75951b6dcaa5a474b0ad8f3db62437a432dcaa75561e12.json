{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */const $65484d02dcb7eb3e$var$DOMPropNames = new Set(['id']);\nconst $65484d02dcb7eb3e$var$labelablePropNames = new Set(['aria-label', 'aria-labelledby', 'aria-describedby', 'aria-details']);\n// See LinkDOMProps in dom.d.ts.\nconst $65484d02dcb7eb3e$var$linkPropNames = new Set(['href', 'hrefLang', 'target', 'rel', 'download', 'ping', 'referrerPolicy']);\nconst $65484d02dcb7eb3e$var$propRe = /^(data-.*)$/;\nfunction $65484d02dcb7eb3e$export$457c3d6518dd4c6f(props, opts = {}) {\n  let {\n    labelable: labelable,\n    isLink: isLink,\n    propNames: propNames\n  } = opts;\n  let filteredProps = {};\n  for (const prop in props) if (Object.prototype.hasOwnProperty.call(props, prop) && ($65484d02dcb7eb3e$var$DOMPropNames.has(prop) || labelable && $65484d02dcb7eb3e$var$labelablePropNames.has(prop) || isLink && $65484d02dcb7eb3e$var$linkPropNames.has(prop) || (propNames === null || propNames === void 0 ? void 0 : propNames.has(prop)) || $65484d02dcb7eb3e$var$propRe.test(prop))) filteredProps[prop] = props[prop];\n  return filteredProps;\n}\nexport { $65484d02dcb7eb3e$export$457c3d6518dd4c6f as filterDOMProps };", "map": {"version": 3, "names": ["$65484d02dcb7eb3e$var$DOMPropNames", "Set", "$65484d02dcb7eb3e$var$labelablePropNames", "$65484d02dcb7eb3e$var$linkPropNames", "$65484d02dcb7eb3e$var$propRe", "$65484d02dcb7eb3e$export$457c3d6518dd4c6f", "props", "opts", "labelable", "isLink", "propNames", "filteredProps", "prop", "Object", "prototype", "hasOwnProperty", "call", "has", "test"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\setting\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\filterDOMProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps, opts: Options = {}): DOMProps & AriaLabelingProps {\n  let {labelable, isLink, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAcA,MAAMA,kCAAA,GAAe,IAAIC,GAAA,CAAI,CAC3B,KACD;AAED,MAAMC,wCAAA,GAAqB,IAAID,GAAA,CAAI,CACjC,cACA,mBACA,oBACA,eACD;AAED;AACA,MAAME,mCAAA,GAAgB,IAAIF,GAAA,CAAI,CAC5B,QACA,YACA,UACA,OACA,YACA,QACA,iBACD;AAeD,MAAMG,4BAAA,GAAS;AAOR,SAASC,0CAAeC,KAAkD,EAAEC,IAAA,GAAgB,CAAC,CAAC;EACnG,IAAI;IAAAC,SAAA,EAACA,SAAS;IAAAC,MAAA,EAAEA,MAAM;IAAAC,SAAA,EAAEA;EAAS,CAAC,GAAGH,IAAA;EACrC,IAAII,aAAA,GAAgB,CAAC;EAErB,KAAK,MAAMC,IAAA,IAAQN,KAAA,EACjB,IACEO,MAAA,CAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,KAAA,EAAOM,IAAA,MAC1CZ,kCAAA,CAAaiB,GAAG,CAACL,IAAA,KAChBJ,SAAA,IAAaN,wCAAA,CAAmBe,GAAG,CAACL,IAAA,KACpCH,MAAA,IAAUN,mCAAA,CAAcc,GAAG,CAACL,IAAA,MAC7BF,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAWO,GAAG,CAACL,IAAA,MACfR,4BAAA,CAAOc,IAAI,CAACN,IAAA,CAAI,GAGlBD,aAAa,CAACC,IAAA,CAAK,GAAGN,KAAK,CAACM,IAAA,CAAK;EAIrC,OAAOD,aAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}