{"ast": null, "code": "import { autoUpdate as Z, flip as ee, inner as te, offset as ne, shift as re, size as le, useFloating as oe, useInnerOffset as ie, useInteractions as se } from \"@floating-ui/react\";\nimport * as j from \"react\";\nimport { createContext as _, use<PERSON>allback as ae, useContext as T, useMemo as R, useRef as ue, useState as v } from \"react\";\nimport { useDisposables as fe } from '../hooks/use-disposables.js';\nimport { useEvent as z } from '../hooks/use-event.js';\nimport { useIsoMorphicEffect as C } from '../hooks/use-iso-morphic-effect.js';\nimport * as pe from '../utils/dom.js';\nlet y = _({\n  styles: void 0,\n  setReference: () => {},\n  setFloating: () => {},\n  getReferenceProps: () => ({}),\n  getFloatingProps: () => ({}),\n  slot: {}\n});\ny.displayName = \"FloatingContext\";\nlet $ = _(null);\n$.displayName = \"PlacementContext\";\nfunction ye(e) {\n  return R(() => e ? typeof e == \"string\" ? {\n    to: e\n  } : e : null, [e]);\n}\nfunction Fe() {\n  return T(y).setReference;\n}\nfunction be() {\n  return T(y).getReferenceProps;\n}\nfunction Te() {\n  let {\n    getFloatingProps: e,\n    slot: t\n  } = T(y);\n  return ae((...n) => Object.assign({}, e(...n), {\n    \"data-anchor\": t.anchor\n  }), [e, t]);\n}\nfunction Re(e = null) {\n  e === !1 && (e = null), typeof e == \"string\" && (e = {\n    to: e\n  });\n  let t = T($),\n    n = R(() => e, [JSON.stringify(e, (l, o) => {\n      var u;\n      return (u = o == null ? void 0 : o.outerHTML) != null ? u : o;\n    })]);\n  C(() => {\n    t == null || t(n != null ? n : null);\n  }, [t, n]);\n  let r = T(y);\n  return R(() => [r.setFloating, e ? r.styles : {}], [r.setFloating, e, r.styles]);\n}\nlet D = 4;\nfunction Ae({\n  children: e,\n  enabled: t = !0\n}) {\n  let [n, r] = v(null),\n    [l, o] = v(0),\n    u = ue(null),\n    [f, s] = v(null);\n  ce(f);\n  let i = t && n !== null && f !== null,\n    {\n      to: F = \"bottom\",\n      gap: E = 0,\n      offset: A = 0,\n      padding: c = 0,\n      inner: h\n    } = ge(n, f),\n    [a, p = \"center\"] = F.split(\" \");\n  C(() => {\n    i && o(0);\n  }, [i]);\n  let {\n      refs: b,\n      floatingStyles: S,\n      context: g\n    } = oe({\n      open: i,\n      placement: a === \"selection\" ? p === \"center\" ? \"bottom\" : `bottom-${p}` : p === \"center\" ? `${a}` : `${a}-${p}`,\n      strategy: \"absolute\",\n      transform: !1,\n      middleware: [ne({\n        mainAxis: a === \"selection\" ? 0 : E,\n        crossAxis: A\n      }), re({\n        padding: c\n      }), a !== \"selection\" && ee({\n        padding: c\n      }), a === \"selection\" && h ? te({\n        ...h,\n        padding: c,\n        overflowRef: u,\n        offset: l,\n        minItemsVisible: D,\n        referenceOverflowThreshold: c,\n        onFallbackChange(P) {\n          var L, N;\n          if (!P) return;\n          let d = g.elements.floating;\n          if (!d) return;\n          let M = parseFloat(getComputedStyle(d).scrollPaddingBottom) || 0,\n            I = Math.min(D, d.childElementCount),\n            W = 0,\n            B = 0;\n          for (let m of (N = (L = g.elements.floating) == null ? void 0 : L.childNodes) != null ? N : []) if (pe.isHTMLElement(m)) {\n            let x = m.offsetTop,\n              k = x + m.clientHeight + M,\n              H = d.scrollTop,\n              U = H + d.clientHeight;\n            if (x >= H && k <= U) I--;else {\n              B = Math.max(0, Math.min(k, U) - Math.max(x, H)), W = m.clientHeight;\n              break;\n            }\n          }\n          I >= 1 && o(m => {\n            let x = W * I - B + M;\n            return m >= x ? m : x;\n          });\n        }\n      }) : null, le({\n        padding: c,\n        apply({\n          availableWidth: P,\n          availableHeight: d,\n          elements: M\n        }) {\n          Object.assign(M.floating.style, {\n            overflow: \"auto\",\n            maxWidth: `${P}px`,\n            maxHeight: `min(var(--anchor-max-height, 100vh), ${d}px)`\n          });\n        }\n      })].filter(Boolean),\n      whileElementsMounted: Z\n    }),\n    [w = a, V = p] = g.placement.split(\"-\");\n  a === \"selection\" && (w = \"selection\");\n  let G = R(() => ({\n      anchor: [w, V].filter(Boolean).join(\" \")\n    }), [w, V]),\n    K = ie(g, {\n      overflowRef: u,\n      onChange: o\n    }),\n    {\n      getReferenceProps: Q,\n      getFloatingProps: X\n    } = se([K]),\n    Y = z(P => {\n      s(P), b.setFloating(P);\n    });\n  return j.createElement($.Provider, {\n    value: r\n  }, j.createElement(y.Provider, {\n    value: {\n      setFloating: Y,\n      setReference: b.setReference,\n      styles: S,\n      getReferenceProps: Q,\n      getFloatingProps: X,\n      slot: G\n    }\n  }, e));\n}\nfunction ce(e) {\n  C(() => {\n    if (!e) return;\n    let t = new MutationObserver(() => {\n      let n = window.getComputedStyle(e).maxHeight,\n        r = parseFloat(n);\n      if (isNaN(r)) return;\n      let l = parseInt(n);\n      isNaN(l) || r !== l && (e.style.maxHeight = `${Math.ceil(r)}px`);\n    });\n    return t.observe(e, {\n      attributes: !0,\n      attributeFilter: [\"style\"]\n    }), () => {\n      t.disconnect();\n    };\n  }, [e]);\n}\nfunction ge(e, t) {\n  var o, u, f;\n  let n = O((o = e == null ? void 0 : e.gap) != null ? o : \"var(--anchor-gap, 0)\", t),\n    r = O((u = e == null ? void 0 : e.offset) != null ? u : \"var(--anchor-offset, 0)\", t),\n    l = O((f = e == null ? void 0 : e.padding) != null ? f : \"var(--anchor-padding, 0)\", t);\n  return {\n    ...e,\n    gap: n,\n    offset: r,\n    padding: l\n  };\n}\nfunction O(e, t, n = void 0) {\n  let r = fe(),\n    l = z((s, i) => {\n      if (s == null) return [n, null];\n      if (typeof s == \"number\") return [s, null];\n      if (typeof s == \"string\") {\n        if (!i) return [n, null];\n        let F = J(s, i);\n        return [F, E => {\n          let A = q(s);\n          {\n            let c = A.map(h => window.getComputedStyle(i).getPropertyValue(h));\n            r.requestAnimationFrame(function h() {\n              r.nextFrame(h);\n              let a = !1;\n              for (let [b, S] of A.entries()) {\n                let g = window.getComputedStyle(i).getPropertyValue(S);\n                if (c[b] !== g) {\n                  c[b] = g, a = !0;\n                  break;\n                }\n              }\n              if (!a) return;\n              let p = J(s, i);\n              F !== p && (E(p), F = p);\n            });\n          }\n          return r.dispose;\n        }];\n      }\n      return [n, null];\n    }),\n    o = R(() => l(e, t)[0], [e, t]),\n    [u = o, f] = v();\n  return C(() => {\n    let [s, i] = l(e, t);\n    if (f(s), !!i) return i(f);\n  }, [e, t]), u;\n}\nfunction q(e) {\n  let t = /var\\((.*)\\)/.exec(e);\n  if (t) {\n    let n = t[1].indexOf(\",\");\n    if (n === -1) return [t[1]];\n    let r = t[1].slice(0, n).trim(),\n      l = t[1].slice(n + 1).trim();\n    return l ? [r, ...q(l)] : [r];\n  }\n  return [];\n}\nfunction J(e, t) {\n  let n = document.createElement(\"div\");\n  t.appendChild(n), n.style.setProperty(\"margin-top\", \"0px\", \"important\"), n.style.setProperty(\"margin-top\", e, \"important\");\n  let r = parseFloat(window.getComputedStyle(n).marginTop) || 0;\n  return t.removeChild(n), r;\n}\nexport { Ae as FloatingProvider, Re as useFloatingPanel, Te as useFloatingPanelProps, Fe as useFloatingReference, be as useFloatingReferenceProps, ye as useResolvedAnchor };", "map": {"version": 3, "names": ["autoUpdate", "Z", "flip", "ee", "inner", "te", "offset", "ne", "shift", "re", "size", "le", "useFloating", "oe", "useInnerOffset", "ie", "useInteractions", "se", "j", "createContext", "_", "useCallback", "ae", "useContext", "T", "useMemo", "R", "useRef", "ue", "useState", "v", "useDisposables", "fe", "useEvent", "z", "useIsoMorphicEffect", "C", "pe", "y", "styles", "setReference", "setFloating", "getReferenceProps", "getFloatingProps", "slot", "displayName", "$", "ye", "e", "to", "Fe", "be", "Te", "t", "n", "Object", "assign", "anchor", "Re", "JSON", "stringify", "l", "o", "u", "outerHTML", "r", "D", "Ae", "children", "enabled", "f", "s", "ce", "i", "F", "gap", "E", "A", "padding", "c", "h", "ge", "a", "p", "split", "refs", "b", "floatingStyles", "S", "context", "g", "open", "placement", "strategy", "transform", "middleware", "mainAxis", "crossAxis", "overflowRef", "minItemsVisible", "referenceOverflowThreshold", "onFallbackChange", "P", "L", "N", "d", "elements", "floating", "M", "parseFloat", "getComputedStyle", "scrollPaddingBottom", "I", "Math", "min", "childElementCount", "W", "B", "m", "childNodes", "isHTMLElement", "x", "offsetTop", "k", "clientHeight", "H", "scrollTop", "U", "max", "apply", "availableWidth", "availableHeight", "style", "overflow", "max<PERSON><PERSON><PERSON>", "maxHeight", "filter", "Boolean", "whileElementsMounted", "w", "V", "G", "join", "K", "onChange", "Q", "X", "Y", "createElement", "Provider", "value", "MutationObserver", "window", "isNaN", "parseInt", "ceil", "observe", "attributes", "attributeFilter", "disconnect", "O", "J", "q", "map", "getPropertyValue", "requestAnimationFrame", "next<PERSON><PERSON><PERSON>", "entries", "dispose", "exec", "indexOf", "slice", "trim", "document", "append<PERSON><PERSON><PERSON>", "setProperty", "marginTop", "<PERSON><PERSON><PERSON><PERSON>", "FloatingProvider", "useFloatingPanel", "useFloatingPanelProps", "useFloatingReference", "useFloatingReferenceProps", "useResolvedAnchor"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/internal/floating.js"], "sourcesContent": ["import{autoUpdate as Z,flip as ee,inner as te,offset as ne,shift as re,size as le,useFloating as oe,useInnerOffset as ie,useInteractions as se}from\"@floating-ui/react\";import*as j from\"react\";import{createContext as _,use<PERSON><PERSON>back as ae,useContext as T,useMemo as R,useRef as ue,useState as v}from\"react\";import{useDisposables as fe}from'../hooks/use-disposables.js';import{useEvent as z}from'../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../hooks/use-iso-morphic-effect.js';import*as pe from'../utils/dom.js';let y=_({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});y.displayName=\"FloatingContext\";let $=_(null);$.displayName=\"PlacementContext\";function ye(e){return R(()=>e?typeof e==\"string\"?{to:e}:e:null,[e])}function Fe(){return T(y).setReference}function be(){return T(y).getReferenceProps}function Te(){let{getFloatingProps:e,slot:t}=T(y);return ae((...n)=>Object.assign({},e(...n),{\"data-anchor\":t.anchor}),[e,t])}function Re(e=null){e===!1&&(e=null),typeof e==\"string\"&&(e={to:e});let t=T($),n=R(()=>e,[JSON.stringify(e,(l,o)=>{var u;return(u=o==null?void 0:o.outerHTML)!=null?u:o})]);C(()=>{t==null||t(n!=null?n:null)},[t,n]);let r=T(y);return R(()=>[r.setFloating,e?r.styles:{}],[r.setFloating,e,r.styles])}let D=4;function Ae({children:e,enabled:t=!0}){let[n,r]=v(null),[l,o]=v(0),u=ue(null),[f,s]=v(null);ce(f);let i=t&&n!==null&&f!==null,{to:F=\"bottom\",gap:E=0,offset:A=0,padding:c=0,inner:h}=ge(n,f),[a,p=\"center\"]=F.split(\" \");C(()=>{i&&o(0)},[i]);let{refs:b,floatingStyles:S,context:g}=oe({open:i,placement:a===\"selection\"?p===\"center\"?\"bottom\":`bottom-${p}`:p===\"center\"?`${a}`:`${a}-${p}`,strategy:\"absolute\",transform:!1,middleware:[ne({mainAxis:a===\"selection\"?0:E,crossAxis:A}),re({padding:c}),a!==\"selection\"&&ee({padding:c}),a===\"selection\"&&h?te({...h,padding:c,overflowRef:u,offset:l,minItemsVisible:D,referenceOverflowThreshold:c,onFallbackChange(P){var L,N;if(!P)return;let d=g.elements.floating;if(!d)return;let M=parseFloat(getComputedStyle(d).scrollPaddingBottom)||0,I=Math.min(D,d.childElementCount),W=0,B=0;for(let m of(N=(L=g.elements.floating)==null?void 0:L.childNodes)!=null?N:[])if(pe.isHTMLElement(m)){let x=m.offsetTop,k=x+m.clientHeight+M,H=d.scrollTop,U=H+d.clientHeight;if(x>=H&&k<=U)I--;else{B=Math.max(0,Math.min(k,U)-Math.max(x,H)),W=m.clientHeight;break}}I>=1&&o(m=>{let x=W*I-B+M;return m>=x?m:x})}}):null,le({padding:c,apply({availableWidth:P,availableHeight:d,elements:M}){Object.assign(M.floating.style,{overflow:\"auto\",maxWidth:`${P}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${d}px)`})}})].filter(Boolean),whileElementsMounted:Z}),[w=a,V=p]=g.placement.split(\"-\");a===\"selection\"&&(w=\"selection\");let G=R(()=>({anchor:[w,V].filter(Boolean).join(\" \")}),[w,V]),K=ie(g,{overflowRef:u,onChange:o}),{getReferenceProps:Q,getFloatingProps:X}=se([K]),Y=z(P=>{s(P),b.setFloating(P)});return j.createElement($.Provider,{value:r},j.createElement(y.Provider,{value:{setFloating:Y,setReference:b.setReference,styles:S,getReferenceProps:Q,getFloatingProps:X,slot:G}},e))}function ce(e){C(()=>{if(!e)return;let t=new MutationObserver(()=>{let n=window.getComputedStyle(e).maxHeight,r=parseFloat(n);if(isNaN(r))return;let l=parseInt(n);isNaN(l)||r!==l&&(e.style.maxHeight=`${Math.ceil(r)}px`)});return t.observe(e,{attributes:!0,attributeFilter:[\"style\"]}),()=>{t.disconnect()}},[e])}function ge(e,t){var o,u,f;let n=O((o=e==null?void 0:e.gap)!=null?o:\"var(--anchor-gap, 0)\",t),r=O((u=e==null?void 0:e.offset)!=null?u:\"var(--anchor-offset, 0)\",t),l=O((f=e==null?void 0:e.padding)!=null?f:\"var(--anchor-padding, 0)\",t);return{...e,gap:n,offset:r,padding:l}}function O(e,t,n=void 0){let r=fe(),l=z((s,i)=>{if(s==null)return[n,null];if(typeof s==\"number\")return[s,null];if(typeof s==\"string\"){if(!i)return[n,null];let F=J(s,i);return[F,E=>{let A=q(s);{let c=A.map(h=>window.getComputedStyle(i).getPropertyValue(h));r.requestAnimationFrame(function h(){r.nextFrame(h);let a=!1;for(let[b,S]of A.entries()){let g=window.getComputedStyle(i).getPropertyValue(S);if(c[b]!==g){c[b]=g,a=!0;break}}if(!a)return;let p=J(s,i);F!==p&&(E(p),F=p)})}return r.dispose}]}return[n,null]}),o=R(()=>l(e,t)[0],[e,t]),[u=o,f]=v();return C(()=>{let[s,i]=l(e,t);if(f(s),!!i)return i(f)},[e,t]),u}function q(e){let t=/var\\((.*)\\)/.exec(e);if(t){let n=t[1].indexOf(\",\");if(n===-1)return[t[1]];let r=t[1].slice(0,n).trim(),l=t[1].slice(n+1).trim();return l?[r,...q(l)]:[r]}return[]}function J(e,t){let n=document.createElement(\"div\");t.appendChild(n),n.style.setProperty(\"margin-top\",\"0px\",\"important\"),n.style.setProperty(\"margin-top\",e,\"important\");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}export{Ae as FloatingProvider,Re as useFloatingPanel,Te as useFloatingPanelProps,Fe as useFloatingReference,be as useFloatingReferenceProps,ye as useResolvedAnchor};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,EAACC,IAAI,IAAIC,EAAE,EAACC,KAAK,IAAIC,EAAE,EAACC,MAAM,IAAIC,EAAE,EAACC,KAAK,IAAIC,EAAE,EAACC,IAAI,IAAIC,EAAE,EAACC,WAAW,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,EAACC,eAAe,IAAIC,EAAE,QAAK,oBAAoB;AAAC,OAAM,KAAIC,CAAC,MAAK,OAAO;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,WAAW,IAAIC,EAAE,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,EAAE,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,6BAA6B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,oCAAoC;AAAC,OAAM,KAAIC,EAAE,MAAK,iBAAiB;AAAC,IAAIC,CAAC,GAAClB,CAAC,CAAC;EAACmB,MAAM,EAAC,KAAK,CAAC;EAACC,YAAY,EAACA,CAAA,KAAI,CAAC,CAAC;EAACC,WAAW,EAACA,CAAA,KAAI,CAAC,CAAC;EAACC,iBAAiB,EAACA,CAAA,MAAK,CAAC,CAAC,CAAC;EAACC,gBAAgB,EAACA,CAAA,MAAK,CAAC,CAAC,CAAC;EAACC,IAAI,EAAC,CAAC;AAAC,CAAC,CAAC;AAACN,CAAC,CAACO,WAAW,GAAC,iBAAiB;AAAC,IAAIC,CAAC,GAAC1B,CAAC,CAAC,IAAI,CAAC;AAAC0B,CAAC,CAACD,WAAW,GAAC,kBAAkB;AAAC,SAASE,EAAEA,CAACC,CAAC,EAAC;EAAC,OAAOtB,CAAC,CAAC,MAAIsB,CAAC,GAAC,OAAOA,CAAC,IAAE,QAAQ,GAAC;IAACC,EAAE,EAACD;EAAC,CAAC,GAACA,CAAC,GAAC,IAAI,EAAC,CAACA,CAAC,CAAC,CAAC;AAAA;AAAC,SAASE,EAAEA,CAAA,EAAE;EAAC,OAAO1B,CAAC,CAACc,CAAC,CAAC,CAACE,YAAY;AAAA;AAAC,SAASW,EAAEA,CAAA,EAAE;EAAC,OAAO3B,CAAC,CAACc,CAAC,CAAC,CAACI,iBAAiB;AAAA;AAAC,SAASU,EAAEA,CAAA,EAAE;EAAC,IAAG;IAACT,gBAAgB,EAACK,CAAC;IAACJ,IAAI,EAACS;EAAC,CAAC,GAAC7B,CAAC,CAACc,CAAC,CAAC;EAAC,OAAOhB,EAAE,CAAC,CAAC,GAAGgC,CAAC,KAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAACR,CAAC,CAAC,GAAGM,CAAC,CAAC,EAAC;IAAC,aAAa,EAACD,CAAC,CAACI;EAAM,CAAC,CAAC,EAAC,CAACT,CAAC,EAACK,CAAC,CAAC,CAAC;AAAA;AAAC,SAASK,EAAEA,CAACV,CAAC,GAAC,IAAI,EAAC;EAACA,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC,OAAOA,CAAC,IAAE,QAAQ,KAAGA,CAAC,GAAC;IAACC,EAAE,EAACD;EAAC,CAAC,CAAC;EAAC,IAAIK,CAAC,GAAC7B,CAAC,CAACsB,CAAC,CAAC;IAACQ,CAAC,GAAC5B,CAAC,CAAC,MAAIsB,CAAC,EAAC,CAACW,IAAI,CAACC,SAAS,CAACZ,CAAC,EAAC,CAACa,CAAC,EAACC,CAAC,KAAG;MAAC,IAAIC,CAAC;MAAC,OAAM,CAACA,CAAC,GAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACE,SAAS,KAAG,IAAI,GAACD,CAAC,GAACD,CAAC;IAAA,CAAC,CAAC,CAAC,CAAC;EAAC1B,CAAC,CAAC,MAAI;IAACiB,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACC,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,IAAI,CAAC;EAAA,CAAC,EAAC,CAACD,CAAC,EAACC,CAAC,CAAC,CAAC;EAAC,IAAIW,CAAC,GAACzC,CAAC,CAACc,CAAC,CAAC;EAAC,OAAOZ,CAAC,CAAC,MAAI,CAACuC,CAAC,CAACxB,WAAW,EAACO,CAAC,GAACiB,CAAC,CAAC1B,MAAM,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC0B,CAAC,CAACxB,WAAW,EAACO,CAAC,EAACiB,CAAC,CAAC1B,MAAM,CAAC,CAAC;AAAA;AAAC,IAAI2B,CAAC,GAAC,CAAC;AAAC,SAASC,EAAEA,CAAC;EAACC,QAAQ,EAACpB,CAAC;EAACqB,OAAO,EAAChB,CAAC,GAAC,CAAC;AAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACW,CAAC,CAAC,GAACnC,CAAC,CAAC,IAAI,CAAC;IAAC,CAAC+B,CAAC,EAACC,CAAC,CAAC,GAAChC,CAAC,CAAC,CAAC,CAAC;IAACiC,CAAC,GAACnC,EAAE,CAAC,IAAI,CAAC;IAAC,CAAC0C,CAAC,EAACC,CAAC,CAAC,GAACzC,CAAC,CAAC,IAAI,CAAC;EAAC0C,EAAE,CAACF,CAAC,CAAC;EAAC,IAAIG,CAAC,GAACpB,CAAC,IAAEC,CAAC,KAAG,IAAI,IAAEgB,CAAC,KAAG,IAAI;IAAC;MAACrB,EAAE,EAACyB,CAAC,GAAC,QAAQ;MAACC,GAAG,EAACC,CAAC,GAAC,CAAC;MAACtE,MAAM,EAACuE,CAAC,GAAC,CAAC;MAACC,OAAO,EAACC,CAAC,GAAC,CAAC;MAAC3E,KAAK,EAAC4E;IAAC,CAAC,GAACC,EAAE,CAAC3B,CAAC,EAACgB,CAAC,CAAC;IAAC,CAACY,CAAC,EAACC,CAAC,GAAC,QAAQ,CAAC,GAACT,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC;EAAChD,CAAC,CAAC,MAAI;IAACqC,CAAC,IAAEX,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACW,CAAC,CAAC,CAAC;EAAC,IAAG;MAACY,IAAI,EAACC,CAAC;MAACC,cAAc,EAACC,CAAC;MAACC,OAAO,EAACC;IAAC,CAAC,GAAC7E,EAAE,CAAC;MAAC8E,IAAI,EAAClB,CAAC;MAACmB,SAAS,EAACV,CAAC,KAAG,WAAW,GAACC,CAAC,KAAG,QAAQ,GAAC,QAAQ,GAAC,UAAUA,CAAC,EAAE,GAACA,CAAC,KAAG,QAAQ,GAAC,GAAGD,CAAC,EAAE,GAAC,GAAGA,CAAC,IAAIC,CAAC,EAAE;MAACU,QAAQ,EAAC,UAAU;MAACC,SAAS,EAAC,CAAC,CAAC;MAACC,UAAU,EAAC,CAACxF,EAAE,CAAC;QAACyF,QAAQ,EAACd,CAAC,KAAG,WAAW,GAAC,CAAC,GAACN,CAAC;QAACqB,SAAS,EAACpB;MAAC,CAAC,CAAC,EAACpE,EAAE,CAAC;QAACqE,OAAO,EAACC;MAAC,CAAC,CAAC,EAACG,CAAC,KAAG,WAAW,IAAE/E,EAAE,CAAC;QAAC2E,OAAO,EAACC;MAAC,CAAC,CAAC,EAACG,CAAC,KAAG,WAAW,IAAEF,CAAC,GAAC3E,EAAE,CAAC;QAAC,GAAG2E,CAAC;QAACF,OAAO,EAACC,CAAC;QAACmB,WAAW,EAACnC,CAAC;QAACzD,MAAM,EAACuD,CAAC;QAACsC,eAAe,EAACjC,CAAC;QAACkC,0BAA0B,EAACrB,CAAC;QAACsB,gBAAgBA,CAACC,CAAC,EAAC;UAAC,IAAIC,CAAC,EAACC,CAAC;UAAC,IAAG,CAACF,CAAC,EAAC;UAAO,IAAIG,CAAC,GAACf,CAAC,CAACgB,QAAQ,CAACC,QAAQ;UAAC,IAAG,CAACF,CAAC,EAAC;UAAO,IAAIG,CAAC,GAACC,UAAU,CAACC,gBAAgB,CAACL,CAAC,CAAC,CAACM,mBAAmB,CAAC,IAAE,CAAC;YAACC,CAAC,GAACC,IAAI,CAACC,GAAG,CAAChD,CAAC,EAACuC,CAAC,CAACU,iBAAiB,CAAC;YAACC,CAAC,GAAC,CAAC;YAACC,CAAC,GAAC,CAAC;UAAC,KAAI,IAAIC,CAAC,IAAG,CAACd,CAAC,GAAC,CAACD,CAAC,GAACb,CAAC,CAACgB,QAAQ,CAACC,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACJ,CAAC,CAACgB,UAAU,KAAG,IAAI,GAACf,CAAC,GAAC,EAAE,EAAC,IAAGnE,EAAE,CAACmF,aAAa,CAACF,CAAC,CAAC,EAAC;YAAC,IAAIG,CAAC,GAACH,CAAC,CAACI,SAAS;cAACC,CAAC,GAACF,CAAC,GAACH,CAAC,CAACM,YAAY,GAAChB,CAAC;cAACiB,CAAC,GAACpB,CAAC,CAACqB,SAAS;cAACC,CAAC,GAACF,CAAC,GAACpB,CAAC,CAACmB,YAAY;YAAC,IAAGH,CAAC,IAAEI,CAAC,IAAEF,CAAC,IAAEI,CAAC,EAACf,CAAC,EAAE,CAAC,KAAI;cAACK,CAAC,GAACJ,IAAI,CAACe,GAAG,CAAC,CAAC,EAACf,IAAI,CAACC,GAAG,CAACS,CAAC,EAACI,CAAC,CAAC,GAACd,IAAI,CAACe,GAAG,CAACP,CAAC,EAACI,CAAC,CAAC,CAAC,EAACT,CAAC,GAACE,CAAC,CAACM,YAAY;cAAC;YAAK;UAAC;UAACZ,CAAC,IAAE,CAAC,IAAElD,CAAC,CAACwD,CAAC,IAAE;YAAC,IAAIG,CAAC,GAACL,CAAC,GAACJ,CAAC,GAACK,CAAC,GAACT,CAAC;YAAC,OAAOU,CAAC,IAAEG,CAAC,GAACH,CAAC,GAACG,CAAC;UAAA,CAAC,CAAC;QAAA;MAAC,CAAC,CAAC,GAAC,IAAI,EAAC9G,EAAE,CAAC;QAACmE,OAAO,EAACC,CAAC;QAACkD,KAAKA,CAAC;UAACC,cAAc,EAAC5B,CAAC;UAAC6B,eAAe,EAAC1B,CAAC;UAACC,QAAQ,EAACE;QAAC,CAAC,EAAC;UAACrD,MAAM,CAACC,MAAM,CAACoD,CAAC,CAACD,QAAQ,CAACyB,KAAK,EAAC;YAACC,QAAQ,EAAC,MAAM;YAACC,QAAQ,EAAC,GAAGhC,CAAC,IAAI;YAACiC,SAAS,EAAC,wCAAwC9B,CAAC;UAAK,CAAC,CAAC;QAAA;MAAC,CAAC,CAAC,CAAC,CAAC+B,MAAM,CAACC,OAAO,CAAC;MAACC,oBAAoB,EAACzI;IAAC,CAAC,CAAC;IAAC,CAAC0I,CAAC,GAACzD,CAAC,EAAC0D,CAAC,GAACzD,CAAC,CAAC,GAACO,CAAC,CAACE,SAAS,CAACR,KAAK,CAAC,GAAG,CAAC;EAACF,CAAC,KAAG,WAAW,KAAGyD,CAAC,GAAC,WAAW,CAAC;EAAC,IAAIE,CAAC,GAACnH,CAAC,CAAC,OAAK;MAAC+B,MAAM,EAAC,CAACkF,CAAC,EAACC,CAAC,CAAC,CAACJ,MAAM,CAACC,OAAO,CAAC,CAACK,IAAI,CAAC,GAAG;IAAC,CAAC,CAAC,EAAC,CAACH,CAAC,EAACC,CAAC,CAAC,CAAC;IAACG,CAAC,GAAChI,EAAE,CAAC2E,CAAC,EAAC;MAACQ,WAAW,EAACnC,CAAC;MAACiF,QAAQ,EAAClF;IAAC,CAAC,CAAC;IAAC;MAACpB,iBAAiB,EAACuG,CAAC;MAACtG,gBAAgB,EAACuG;IAAC,CAAC,GAACjI,EAAE,CAAC,CAAC8H,CAAC,CAAC,CAAC;IAACI,CAAC,GAACjH,CAAC,CAACoE,CAAC,IAAE;MAAC/B,CAAC,CAAC+B,CAAC,CAAC,EAAChB,CAAC,CAAC7C,WAAW,CAAC6D,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAOpF,CAAC,CAACkI,aAAa,CAACtG,CAAC,CAACuG,QAAQ,EAAC;IAACC,KAAK,EAACrF;EAAC,CAAC,EAAC/C,CAAC,CAACkI,aAAa,CAAC9G,CAAC,CAAC+G,QAAQ,EAAC;IAACC,KAAK,EAAC;MAAC7G,WAAW,EAAC0G,CAAC;MAAC3G,YAAY,EAAC8C,CAAC,CAAC9C,YAAY;MAACD,MAAM,EAACiD,CAAC;MAAC9C,iBAAiB,EAACuG,CAAC;MAACtG,gBAAgB,EAACuG,CAAC;MAACtG,IAAI,EAACiG;IAAC;EAAC,CAAC,EAAC7F,CAAC,CAAC,CAAC;AAAA;AAAC,SAASwB,EAAEA,CAACxB,CAAC,EAAC;EAACZ,CAAC,CAAC,MAAI;IAAC,IAAG,CAACY,CAAC,EAAC;IAAO,IAAIK,CAAC,GAAC,IAAIkG,gBAAgB,CAAC,MAAI;MAAC,IAAIjG,CAAC,GAACkG,MAAM,CAAC1C,gBAAgB,CAAC9D,CAAC,CAAC,CAACuF,SAAS;QAACtE,CAAC,GAAC4C,UAAU,CAACvD,CAAC,CAAC;MAAC,IAAGmG,KAAK,CAACxF,CAAC,CAAC,EAAC;MAAO,IAAIJ,CAAC,GAAC6F,QAAQ,CAACpG,CAAC,CAAC;MAACmG,KAAK,CAAC5F,CAAC,CAAC,IAAEI,CAAC,KAAGJ,CAAC,KAAGb,CAAC,CAACoF,KAAK,CAACG,SAAS,GAAC,GAAGtB,IAAI,CAAC0C,IAAI,CAAC1F,CAAC,CAAC,IAAI,CAAC;IAAA,CAAC,CAAC;IAAC,OAAOZ,CAAC,CAACuG,OAAO,CAAC5G,CAAC,EAAC;MAAC6G,UAAU,EAAC,CAAC,CAAC;MAACC,eAAe,EAAC,CAAC,OAAO;IAAC,CAAC,CAAC,EAAC,MAAI;MAACzG,CAAC,CAAC0G,UAAU,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC,EAAC,CAAC/G,CAAC,CAAC,CAAC;AAAA;AAAC,SAASiC,EAAEA,CAACjC,CAAC,EAACK,CAAC,EAAC;EAAC,IAAIS,CAAC,EAACC,CAAC,EAACO,CAAC;EAAC,IAAIhB,CAAC,GAAC0G,CAAC,CAAC,CAAClG,CAAC,GAACd,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC2B,GAAG,KAAG,IAAI,GAACb,CAAC,GAAC,sBAAsB,EAACT,CAAC,CAAC;IAACY,CAAC,GAAC+F,CAAC,CAAC,CAACjG,CAAC,GAACf,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC1C,MAAM,KAAG,IAAI,GAACyD,CAAC,GAAC,yBAAyB,EAACV,CAAC,CAAC;IAACQ,CAAC,GAACmG,CAAC,CAAC,CAAC1F,CAAC,GAACtB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC8B,OAAO,KAAG,IAAI,GAACR,CAAC,GAAC,0BAA0B,EAACjB,CAAC,CAAC;EAAC,OAAM;IAAC,GAAGL,CAAC;IAAC2B,GAAG,EAACrB,CAAC;IAAChD,MAAM,EAAC2D,CAAC;IAACa,OAAO,EAACjB;EAAC,CAAC;AAAA;AAAC,SAASmG,CAACA,CAAChH,CAAC,EAACK,CAAC,EAACC,CAAC,GAAC,KAAK,CAAC,EAAC;EAAC,IAAIW,CAAC,GAACjC,EAAE,CAAC,CAAC;IAAC6B,CAAC,GAAC3B,CAAC,CAAC,CAACqC,CAAC,EAACE,CAAC,KAAG;MAAC,IAAGF,CAAC,IAAE,IAAI,EAAC,OAAM,CAACjB,CAAC,EAAC,IAAI,CAAC;MAAC,IAAG,OAAOiB,CAAC,IAAE,QAAQ,EAAC,OAAM,CAACA,CAAC,EAAC,IAAI,CAAC;MAAC,IAAG,OAAOA,CAAC,IAAE,QAAQ,EAAC;QAAC,IAAG,CAACE,CAAC,EAAC,OAAM,CAACnB,CAAC,EAAC,IAAI,CAAC;QAAC,IAAIoB,CAAC,GAACuF,CAAC,CAAC1F,CAAC,EAACE,CAAC,CAAC;QAAC,OAAM,CAACC,CAAC,EAACE,CAAC,IAAE;UAAC,IAAIC,CAAC,GAACqF,CAAC,CAAC3F,CAAC,CAAC;UAAC;YAAC,IAAIQ,CAAC,GAACF,CAAC,CAACsF,GAAG,CAACnF,CAAC,IAAEwE,MAAM,CAAC1C,gBAAgB,CAACrC,CAAC,CAAC,CAAC2F,gBAAgB,CAACpF,CAAC,CAAC,CAAC;YAACf,CAAC,CAACoG,qBAAqB,CAAC,SAASrF,CAACA,CAAA,EAAE;cAACf,CAAC,CAACqG,SAAS,CAACtF,CAAC,CAAC;cAAC,IAAIE,CAAC,GAAC,CAAC,CAAC;cAAC,KAAI,IAAG,CAACI,CAAC,EAACE,CAAC,CAAC,IAAGX,CAAC,CAAC0F,OAAO,CAAC,CAAC,EAAC;gBAAC,IAAI7E,CAAC,GAAC8D,MAAM,CAAC1C,gBAAgB,CAACrC,CAAC,CAAC,CAAC2F,gBAAgB,CAAC5E,CAAC,CAAC;gBAAC,IAAGT,CAAC,CAACO,CAAC,CAAC,KAAGI,CAAC,EAAC;kBAACX,CAAC,CAACO,CAAC,CAAC,GAACI,CAAC,EAACR,CAAC,GAAC,CAAC,CAAC;kBAAC;gBAAK;cAAC;cAAC,IAAG,CAACA,CAAC,EAAC;cAAO,IAAIC,CAAC,GAAC8E,CAAC,CAAC1F,CAAC,EAACE,CAAC,CAAC;cAACC,CAAC,KAAGS,CAAC,KAAGP,CAAC,CAACO,CAAC,CAAC,EAACT,CAAC,GAACS,CAAC,CAAC;YAAA,CAAC,CAAC;UAAA;UAAC,OAAOlB,CAAC,CAACuG,OAAO;QAAA,CAAC,CAAC;MAAA;MAAC,OAAM,CAAClH,CAAC,EAAC,IAAI,CAAC;IAAA,CAAC,CAAC;IAACQ,CAAC,GAACpC,CAAC,CAAC,MAAImC,CAAC,CAACb,CAAC,EAACK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACL,CAAC,EAACK,CAAC,CAAC,CAAC;IAAC,CAACU,CAAC,GAACD,CAAC,EAACQ,CAAC,CAAC,GAACxC,CAAC,CAAC,CAAC;EAAC,OAAOM,CAAC,CAAC,MAAI;IAAC,IAAG,CAACmC,CAAC,EAACE,CAAC,CAAC,GAACZ,CAAC,CAACb,CAAC,EAACK,CAAC,CAAC;IAAC,IAAGiB,CAAC,CAACC,CAAC,CAAC,EAAC,CAAC,CAACE,CAAC,EAAC,OAAOA,CAAC,CAACH,CAAC,CAAC;EAAA,CAAC,EAAC,CAACtB,CAAC,EAACK,CAAC,CAAC,CAAC,EAACU,CAAC;AAAA;AAAC,SAASmG,CAACA,CAAClH,CAAC,EAAC;EAAC,IAAIK,CAAC,GAAC,aAAa,CAACoH,IAAI,CAACzH,CAAC,CAAC;EAAC,IAAGK,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAAC,CAAC,CAAC,CAACqH,OAAO,CAAC,GAAG,CAAC;IAAC,IAAGpH,CAAC,KAAG,CAAC,CAAC,EAAC,OAAM,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,IAAIY,CAAC,GAACZ,CAAC,CAAC,CAAC,CAAC,CAACsH,KAAK,CAAC,CAAC,EAACrH,CAAC,CAAC,CAACsH,IAAI,CAAC,CAAC;MAAC/G,CAAC,GAACR,CAAC,CAAC,CAAC,CAAC,CAACsH,KAAK,CAACrH,CAAC,GAAC,CAAC,CAAC,CAACsH,IAAI,CAAC,CAAC;IAAC,OAAO/G,CAAC,GAAC,CAACI,CAAC,EAAC,GAAGiG,CAAC,CAACrG,CAAC,CAAC,CAAC,GAAC,CAACI,CAAC,CAAC;EAAA;EAAC,OAAM,EAAE;AAAA;AAAC,SAASgG,CAACA,CAACjH,CAAC,EAACK,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACuH,QAAQ,CAACzB,aAAa,CAAC,KAAK,CAAC;EAAC/F,CAAC,CAACyH,WAAW,CAACxH,CAAC,CAAC,EAACA,CAAC,CAAC8E,KAAK,CAAC2C,WAAW,CAAC,YAAY,EAAC,KAAK,EAAC,WAAW,CAAC,EAACzH,CAAC,CAAC8E,KAAK,CAAC2C,WAAW,CAAC,YAAY,EAAC/H,CAAC,EAAC,WAAW,CAAC;EAAC,IAAIiB,CAAC,GAAC4C,UAAU,CAAC2C,MAAM,CAAC1C,gBAAgB,CAACxD,CAAC,CAAC,CAAC0H,SAAS,CAAC,IAAE,CAAC;EAAC,OAAO3H,CAAC,CAAC4H,WAAW,CAAC3H,CAAC,CAAC,EAACW,CAAC;AAAA;AAAC,SAAOE,EAAE,IAAI+G,gBAAgB,EAACxH,EAAE,IAAIyH,gBAAgB,EAAC/H,EAAE,IAAIgI,qBAAqB,EAAClI,EAAE,IAAImI,oBAAoB,EAAClI,EAAE,IAAImI,yBAAyB,EAACvI,EAAE,IAAIwI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}