{"ast": null, "code": "import { useDocumentOverflowLockedEffect as l } from './document-overflow/use-document-overflow.js';\nimport { useIsTopLayer as m } from './use-is-top-layer.js';\nfunction f(e, c, n = () => [document.body]) {\n  let r = m(e, \"scroll-lock\");\n  l(r, c, t => {\n    var o;\n    return {\n      containers: [...((o = t.containers) != null ? o : []), n]\n    };\n  });\n}\nexport { f as useScrollLock };", "map": {"version": 3, "names": ["useDocumentOverflowLockedEffect", "l", "useIsTopLayer", "m", "f", "e", "c", "n", "document", "body", "r", "t", "o", "containers", "useScrollLock"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-scroll-lock.js"], "sourcesContent": ["import{useDocumentOverflowLockedEffect as l}from'./document-overflow/use-document-overflow.js';import{useIsTopLayer as m}from'./use-is-top-layer.js';function f(e,c,n=()=>[document.body]){let r=m(e,\"scroll-lock\");l(r,c,t=>{var o;return{containers:[...(o=t.containers)!=null?o:[],n]}})}export{f as useScrollLock};\n"], "mappings": "AAAA,SAAOA,+BAA+B,IAAIC,CAAC,QAAK,8CAA8C;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,GAACA,CAAA,KAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACP,CAAC,CAACE,CAAC,EAAC,aAAa,CAAC;EAACJ,CAAC,CAACS,CAAC,EAACJ,CAAC,EAACK,CAAC,IAAE;IAAC,IAAIC,CAAC;IAAC,OAAM;MAACC,UAAU,EAAC,CAAC,IAAG,CAACD,CAAC,GAACD,CAAC,CAACE,UAAU,KAAG,IAAI,GAACD,CAAC,GAAC,EAAE,GAACL,CAAC;IAAC,CAAC;EAAA,CAAC,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAIU,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}