const Profit = require('../models/Profit');

// GET profit by event
const getProfitByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const profits = await Profit.find({ eventId })
      .sort({ date: -1 });
    
    const totalProfit = profits.reduce((sum, profit) => sum + profit.amount, 0);
    
    res.json({ 
      success: true, 
      data: {
        profits,
        totalProfit,
        count: profits.length
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// POST record profit
const recordProfit = async (req, res) => {
  try {
    const { eventId, amount } = req.body;
    
    const profit = new Profit({
      eventId,
      amount
    });
    
    await profit.save();
    
    res.status(201).json({ 
      success: true, 
      message: 'Profit recorded successfully',
      data: profit 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// GET total profit across all events
const getTotalProfit = async (req, res) => {
  try {
    const profits = await Profit.find();
    const totalProfit = profits.reduce((sum, profit) => sum + profit.amount, 0);
    
    res.json({ 
      success: true, 
      data: {
        totalProfit,
        recordCount: profits.length
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

// GET profit analytics
const getProfitAnalytics = async (req, res) => {
  try {
    const profits = await Profit.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': -1, '_id.month': -1 }
      }
    ]);
    
    res.json({ 
      success: true, 
      data: profits 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
};

module.exports = {
  getProfitByEvent,
  recordProfit,
  getTotalProfit,
  getProfitAnalytics
};
