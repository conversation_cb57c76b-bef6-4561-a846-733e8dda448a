{"ast": null, "code": "import { useId as r } from \"react\";\nexport { r as useId };", "map": {"version": 3, "names": ["useId", "r"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-id.js"], "sourcesContent": ["import{useId as r}from\"react\";export{r as useId};\n"], "mappings": "AAAA,SAAOA,KAAK,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOA,CAAC,IAAID,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}