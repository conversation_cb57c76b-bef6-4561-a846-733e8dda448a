{"ast": null, "code": "var T, b;\nimport { useRef as c, useState as S } from \"react\";\nimport { disposables as m } from '../utils/disposables.js';\nimport { useDisposables as g } from './use-disposables.js';\nimport { useFlags as y } from './use-flags.js';\nimport { useIsoMorphicEffect as A } from './use-iso-morphic-effect.js';\ntypeof process != \"undefined\" && typeof globalThis != \"undefined\" && typeof Element != \"undefined\" && ((T = process == null ? void 0 : process.env) == null ? void 0 : T[\"NODE_ENV\"]) === \"test\" && typeof ((b = Element == null ? void 0 : Element.prototype) == null ? void 0 : b.getAnimations) == \"undefined\" && (Element.prototype.getAnimations = function () {\n  return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\", \"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\", \"\", \"Example usage:\", \"```js\", \"import { mockAnimationsApi } from 'jsdom-testing-mocks'\", \"mockAnimationsApi()\", \"```\"].join(`\n`)), [];\n});\nvar L = (r => (r[r.None = 0] = \"None\", r[r.Closed = 1] = \"Closed\", r[r.Enter = 2] = \"Enter\", r[r.Leave = 4] = \"Leave\", r))(L || {});\nfunction R(t) {\n  let n = {};\n  for (let e in t) t[e] === !0 && (n[`data-${e}`] = \"\");\n  return n;\n}\nfunction x(t, n, e, i) {\n  let [r, o] = S(e),\n    {\n      hasFlag: s,\n      addFlag: a,\n      removeFlag: l\n    } = y(t && r ? 3 : 0),\n    u = c(!1),\n    f = c(!1),\n    E = g();\n  return A(() => {\n    var d;\n    if (t) {\n      if (e && o(!0), !n) {\n        e && a(3);\n        return;\n      }\n      return (d = i == null ? void 0 : i.start) == null || d.call(i, e), C(n, {\n        inFlight: u,\n        prepare() {\n          f.current ? f.current = !1 : f.current = u.current, u.current = !0, !f.current && (e ? (a(3), l(4)) : (a(4), l(2)));\n        },\n        run() {\n          f.current ? e ? (l(3), a(4)) : (l(4), a(3)) : e ? l(1) : a(1);\n        },\n        done() {\n          var p;\n          f.current && typeof n.getAnimations == \"function\" && n.getAnimations().length > 0 || (u.current = !1, l(7), e || o(!1), (p = i == null ? void 0 : i.end) == null || p.call(i, e));\n        }\n      });\n    }\n  }, [t, e, n, E]), t ? [r, {\n    closed: s(1),\n    enter: s(2),\n    leave: s(4),\n    transition: s(2) || s(4)\n  }] : [e, {\n    closed: void 0,\n    enter: void 0,\n    leave: void 0,\n    transition: void 0\n  }];\n}\nfunction C(t, {\n  prepare: n,\n  run: e,\n  done: i,\n  inFlight: r\n}) {\n  let o = m();\n  return j(t, {\n    prepare: n,\n    inFlight: r\n  }), o.nextFrame(() => {\n    e(), o.requestAnimationFrame(() => {\n      o.add(M(t, i));\n    });\n  }), o.dispose;\n}\nfunction M(t, n) {\n  var o, s;\n  let e = m();\n  if (!t) return e.dispose;\n  let i = !1;\n  e.add(() => {\n    i = !0;\n  });\n  let r = (s = (o = t.getAnimations) == null ? void 0 : o.call(t).filter(a => a instanceof CSSTransition)) != null ? s : [];\n  return r.length === 0 ? (n(), e.dispose) : (Promise.allSettled(r.map(a => a.finished)).then(() => {\n    i || n();\n  }), e.dispose);\n}\nfunction j(t, {\n  inFlight: n,\n  prepare: e\n}) {\n  if (n != null && n.current) {\n    e();\n    return;\n  }\n  let i = t.style.transition;\n  t.style.transition = \"none\", e(), t.offsetHeight, t.style.transition = i;\n}\nexport { R as transitionDataAttributes, x as useTransition };", "map": {"version": 3, "names": ["T", "b", "useRef", "c", "useState", "S", "disposables", "m", "useDisposables", "g", "useFlags", "y", "useIsoMorphicEffect", "A", "process", "globalThis", "Element", "env", "prototype", "getAnimations", "console", "warn", "join", "L", "r", "None", "Closed", "Enter", "Leave", "R", "t", "n", "e", "x", "i", "o", "hasFlag", "s", "addFlag", "a", "removeFlag", "l", "u", "f", "E", "d", "start", "call", "C", "inFlight", "prepare", "current", "run", "done", "p", "length", "end", "closed", "enter", "leave", "transition", "j", "next<PERSON><PERSON><PERSON>", "requestAnimationFrame", "add", "M", "dispose", "filter", "CSSTransition", "Promise", "allSettled", "map", "finished", "then", "style", "offsetHeight", "transitionDataAttributes", "useTransition"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/node_modules/@headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n"], "mappings": "AAAA,IAAIA,CAAC,EAACC,CAAC;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,UAAU,IAAE,WAAW,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,CAAC,CAAChB,CAAC,GAACc,OAAO,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,OAAO,CAACG,GAAG,KAAG,IAAI,GAAC,KAAK,CAAC,GAACjB,CAAC,CAAC,UAAU,CAAC,MAAI,MAAM,IAAE,QAAO,CAACC,CAAC,GAACe,OAAO,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,OAAO,CAACE,SAAS,KAAG,IAAI,GAAC,KAAK,CAAC,GAACjB,CAAC,CAACkB,aAAa,CAAC,IAAE,WAAW,KAAGH,OAAO,CAACE,SAAS,CAACC,aAAa,GAAC,YAAU;EAAC,OAAOC,OAAO,CAACC,IAAI,CAAC,CAAC,8EAA8E,EAAC,yFAAyF,EAAC,EAAE,EAAC,gBAAgB,EAAC,OAAO,EAAC,yDAAyD,EAAC,qBAAqB,EAAC,KAAK,CAAC,CAACC,IAAI,CAAC;AACn3B,CAAC,CAAC,CAAC,EAAC,EAAE;AAAA,CAAC,CAAC;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAACA,CAAC,CAACG,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACH,CAAC,CAACA,CAAC,CAACI,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACJ,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASM,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;EAAC,KAAI,IAAIC,CAAC,IAAIF,CAAC,EAACA,CAAC,CAACE,CAAC,CAAC,KAAG,CAAC,CAAC,KAAGD,CAAC,CAAC,QAAQC,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASE,CAACA,CAACH,CAAC,EAACC,CAAC,EAACC,CAAC,EAACE,CAAC,EAAC;EAAC,IAAG,CAACV,CAAC,EAACW,CAAC,CAAC,GAAC9B,CAAC,CAAC2B,CAAC,CAAC;IAAC;MAACI,OAAO,EAACC,CAAC;MAACC,OAAO,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC9B,CAAC,CAACmB,CAAC,IAAEN,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;IAACkB,CAAC,GAACvC,CAAC,CAAC,CAAC,CAAC,CAAC;IAACwC,CAAC,GAACxC,CAAC,CAAC,CAAC,CAAC,CAAC;IAACyC,CAAC,GAACnC,CAAC,CAAC,CAAC;EAAC,OAAOI,CAAC,CAAC,MAAI;IAAC,IAAIgC,CAAC;IAAC,IAAGf,CAAC,EAAC;MAAC,IAAGE,CAAC,IAAEG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACJ,CAAC,EAAC;QAACC,CAAC,IAAEO,CAAC,CAAC,CAAC,CAAC;QAAC;MAAM;MAAC,OAAM,CAACM,CAAC,GAACX,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACY,KAAK,KAAG,IAAI,IAAED,CAAC,CAACE,IAAI,CAACb,CAAC,EAACF,CAAC,CAAC,EAACgB,CAAC,CAACjB,CAAC,EAAC;QAACkB,QAAQ,EAACP,CAAC;QAACQ,OAAOA,CAAA,EAAE;UAACP,CAAC,CAACQ,OAAO,GAACR,CAAC,CAACQ,OAAO,GAAC,CAAC,CAAC,GAACR,CAAC,CAACQ,OAAO,GAACT,CAAC,CAACS,OAAO,EAACT,CAAC,CAACS,OAAO,GAAC,CAAC,CAAC,EAAC,CAACR,CAAC,CAACQ,OAAO,KAAGnB,CAAC,IAAEO,CAAC,CAAC,CAAC,CAAC,EAACE,CAAC,CAAC,CAAC,CAAC,KAAGF,CAAC,CAAC,CAAC,CAAC,EAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAACW,GAAGA,CAAA,EAAE;UAACT,CAAC,CAACQ,OAAO,GAACnB,CAAC,IAAES,CAAC,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAACP,CAAC,GAACS,CAAC,CAAC,CAAC,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAACc,IAAIA,CAAA,EAAE;UAAC,IAAIC,CAAC;UAACX,CAAC,CAACQ,OAAO,IAAE,OAAOpB,CAAC,CAACZ,aAAa,IAAE,UAAU,IAAEY,CAAC,CAACZ,aAAa,CAAC,CAAC,CAACoC,MAAM,GAAC,CAAC,KAAGb,CAAC,CAACS,OAAO,GAAC,CAAC,CAAC,EAACV,CAAC,CAAC,CAAC,CAAC,EAACT,CAAC,IAAEG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACmB,CAAC,GAACpB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACsB,GAAG,KAAG,IAAI,IAAEF,CAAC,CAACP,IAAI,CAACb,CAAC,EAACF,CAAC,CAAC,CAAC;QAAA;MAAC,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,EAACD,CAAC,EAACa,CAAC,CAAC,CAAC,EAACd,CAAC,GAAC,CAACN,CAAC,EAAC;IAACiC,MAAM,EAACpB,CAAC,CAAC,CAAC,CAAC;IAACqB,KAAK,EAACrB,CAAC,CAAC,CAAC,CAAC;IAACsB,KAAK,EAACtB,CAAC,CAAC,CAAC,CAAC;IAACuB,UAAU,EAACvB,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC;EAAC,CAAC,CAAC,GAAC,CAACL,CAAC,EAAC;IAACyB,MAAM,EAAC,KAAK,CAAC;IAACC,KAAK,EAAC,KAAK,CAAC;IAACC,KAAK,EAAC,KAAK,CAAC;IAACC,UAAU,EAAC,KAAK;EAAC,CAAC,CAAC;AAAA;AAAC,SAASZ,CAACA,CAAClB,CAAC,EAAC;EAACoB,OAAO,EAACnB,CAAC;EAACqB,GAAG,EAACpB,CAAC;EAACqB,IAAI,EAACnB,CAAC;EAACe,QAAQ,EAACzB;AAAC,CAAC,EAAC;EAAC,IAAIW,CAAC,GAAC5B,CAAC,CAAC,CAAC;EAAC,OAAOsD,CAAC,CAAC/B,CAAC,EAAC;IAACoB,OAAO,EAACnB,CAAC;IAACkB,QAAQ,EAACzB;EAAC,CAAC,CAAC,EAACW,CAAC,CAAC2B,SAAS,CAAC,MAAI;IAAC9B,CAAC,CAAC,CAAC,EAACG,CAAC,CAAC4B,qBAAqB,CAAC,MAAI;MAAC5B,CAAC,CAAC6B,GAAG,CAACC,CAAC,CAACnC,CAAC,EAACI,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,CAAC,EAACC,CAAC,CAAC+B,OAAO;AAAA;AAAC,SAASD,CAACA,CAACnC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAII,CAAC,EAACE,CAAC;EAAC,IAAIL,CAAC,GAACzB,CAAC,CAAC,CAAC;EAAC,IAAG,CAACuB,CAAC,EAAC,OAAOE,CAAC,CAACkC,OAAO;EAAC,IAAIhC,CAAC,GAAC,CAAC,CAAC;EAACF,CAAC,CAACgC,GAAG,CAAC,MAAI;IAAC9B,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,IAAIV,CAAC,GAAC,CAACa,CAAC,GAAC,CAACF,CAAC,GAACL,CAAC,CAACX,aAAa,KAAG,IAAI,GAAC,KAAK,CAAC,GAACgB,CAAC,CAACY,IAAI,CAACjB,CAAC,CAAC,CAACqC,MAAM,CAAC5B,CAAC,IAAEA,CAAC,YAAY6B,aAAa,CAAC,KAAG,IAAI,GAAC/B,CAAC,GAAC,EAAE;EAAC,OAAOb,CAAC,CAAC+B,MAAM,KAAG,CAAC,IAAExB,CAAC,CAAC,CAAC,EAACC,CAAC,CAACkC,OAAO,KAAGG,OAAO,CAACC,UAAU,CAAC9C,CAAC,CAAC+C,GAAG,CAAChC,CAAC,IAAEA,CAAC,CAACiC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,MAAI;IAACvC,CAAC,IAAEH,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACC,CAAC,CAACkC,OAAO,CAAC;AAAA;AAAC,SAASL,CAACA,CAAC/B,CAAC,EAAC;EAACmB,QAAQ,EAAClB,CAAC;EAACmB,OAAO,EAAClB;AAAC,CAAC,EAAC;EAAC,IAAGD,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACoB,OAAO,EAAC;IAACnB,CAAC,CAAC,CAAC;IAAC;EAAM;EAAC,IAAIE,CAAC,GAACJ,CAAC,CAAC4C,KAAK,CAACd,UAAU;EAAC9B,CAAC,CAAC4C,KAAK,CAACd,UAAU,GAAC,MAAM,EAAC5B,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC6C,YAAY,EAAC7C,CAAC,CAAC4C,KAAK,CAACd,UAAU,GAAC1B,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAI+C,wBAAwB,EAAC3C,CAAC,IAAI4C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}