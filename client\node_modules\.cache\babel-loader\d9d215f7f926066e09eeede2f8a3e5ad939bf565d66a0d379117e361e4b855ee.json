{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\setting\\\\client\\\\src\\\\components\\\\settings\\\\ProfitSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { CurrencyDollarIcon, ChartBarIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst ProfitSettings = ({\n  settings,\n  onUpdate\n}) => {\n  _s();\n  var _currencies$find, _currencies$find2, _formData$revenueGoal;\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    const newValue = type === 'number' ? parseFloat(value) : value;\n    const newData = {\n      ...formData,\n      [name]: newValue\n    };\n    setFormData(newData);\n  };\n  const handleToggle = setting => {\n    const newData = {\n      ...formData,\n      [setting]: !formData[setting]\n    };\n    setFormData(newData);\n  };\n  const handlePaymentMethodToggle = method => {\n    var _formData$paymentMeth;\n    const newData = {\n      ...formData,\n      paymentMethods: {\n        ...formData.paymentMethods,\n        [method]: !((_formData$paymentMeth = formData.paymentMethods) !== null && _formData$paymentMeth !== void 0 && _formData$paymentMeth[method])\n      }\n    };\n    setFormData(newData);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n  const currencies = [{\n    code: 'USD',\n    name: 'US Dollar',\n    symbol: '$'\n  }, {\n    code: 'EUR',\n    name: 'Euro',\n    symbol: '€'\n  }, {\n    code: 'GBP',\n    name: 'British Pound',\n    symbol: '£'\n  }, {\n    code: 'CAD',\n    name: 'Canadian Dollar',\n    symbol: 'C$'\n  }, {\n    code: 'AUD',\n    name: 'Australian Dollar',\n    symbol: 'A$'\n  }, {\n    code: 'JPY',\n    name: 'Japanese Yen',\n    symbol: '¥'\n  }];\n  const paymentMethods = [{\n    key: 'stripe',\n    name: 'Stripe',\n    description: 'Accept credit cards and digital payments',\n    icon: '💳'\n  }, {\n    key: 'paypal',\n    name: 'PayPal',\n    description: 'Accept PayPal payments',\n    icon: '🅿️'\n  }, {\n    key: 'bankTransfer',\n    name: 'Bank Transfer',\n    description: 'Accept direct bank transfers',\n    icon: '🏦'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Profit & Payment Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: \"Configure payment methods, currency, and profit tracking settings.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(CurrencyDollarIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), \"Currency & Tax Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"currency\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Default Currency\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"currency\",\n              id: \"currency\",\n              value: formData.currency || 'USD',\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n              children: currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: currency.code,\n                children: [currency.symbol, \" \", currency.name, \" (\", currency.code, \")\"]\n              }, currency.code, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"taxRate\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Tax Rate (%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"taxRate\",\n              id: \"taxRate\",\n              value: formData.taxRate || 0,\n              onChange: handleInputChange,\n              disabled: !isEditing,\n              min: \"0\",\n              max: \"100\",\n              step: \"0.1\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"revenueGoal\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Annual Revenue Goal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 sm:text-sm\",\n                  children: ((_currencies$find = currencies.find(c => c.code === formData.currency)) === null || _currencies$find === void 0 ? void 0 : _currencies$find.symbol) || '$'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"revenueGoal\",\n                id: \"revenueGoal\",\n                value: formData.revenueGoal || 0,\n                onChange: handleInputChange,\n                disabled: !isEditing,\n                min: \"0\",\n                className: \"pl-7 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n                placeholder: \"50000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4\",\n          children: \"Payment Methods\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: paymentMethods.map(method => {\n            var _formData$paymentMeth2, _formData$paymentMeth3, _formData$paymentMeth4;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: method.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: method.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500 mt-1\",\n                    children: method.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                checked: ((_formData$paymentMeth2 = formData.paymentMethods) === null || _formData$paymentMeth2 === void 0 ? void 0 : _formData$paymentMeth2[method.key]) || false,\n                onChange: () => handlePaymentMethodToggle(method.key),\n                disabled: !isEditing,\n                className: classNames((_formData$paymentMeth3 = formData.paymentMethods) !== null && _formData$paymentMeth3 !== void 0 && _formData$paymentMeth3[method.key] ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: [\"Enable \", method.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  \"aria-hidden\": \"true\",\n                  className: classNames((_formData$paymentMeth4 = formData.paymentMethods) !== null && _formData$paymentMeth4 !== void 0 && _formData$paymentMeth4[method.key] ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, method.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-base font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ChartBarIcon, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), \"Automation & Reporting\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Auto Invoicing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Automatically generate and send invoices for paid events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.autoInvoicing || false,\n              onChange: () => handleToggle('autoInvoicing'),\n              disabled: !isEditing,\n              className: classNames(formData.autoInvoicing ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Auto invoicing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.autoInvoicing ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Profit Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Generate monthly profit and revenue reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.profitReports || false,\n              onChange: () => handleToggle('profitReports'),\n              disabled: !isEditing,\n              className: classNames(formData.profitReports ? 'bg-blue-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2', !isEditing && 'opacity-50 cursor-not-allowed'),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Profit reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                \"aria-hidden\": \"true\",\n                className: classNames(formData.profitReports ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(ChartBarIcon, {\n              className: \"h-5 w-5 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: \"Revenue Goal Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-blue-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Current goal: \", ((_currencies$find2 = currencies.find(c => c.code === formData.currency)) === null || _currencies$find2 === void 0 ? void 0 : _currencies$find2.symbol) || '$', ((_formData$revenueGoal = formData.revenueGoal) === null || _formData$revenueGoal === void 0 ? void 0 : _formData$revenueGoal.toLocaleString()) || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 bg-blue-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-600 h-2 rounded-full\",\n                  style: {\n                    width: '35%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs\",\n                children: \"35% of annual goal achieved\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setIsEditing(true),\n          className: \"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Edit Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfitSettings, \"rEZs2nirDlaVd6Cwh1oU/4Iupv4=\");\n_c = ProfitSettings;\nexport default ProfitSettings;\nvar _c;\n$RefreshReg$(_c, \"ProfitSettings\");", "map": {"version": 3, "names": ["React", "useState", "Switch", "CurrencyDollarIcon", "ChartBarIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "ProfitSettings", "settings", "onUpdate", "_s", "_currencies$find", "_currencies$find2", "_formData$revenueGoal", "formData", "setFormData", "isEditing", "setIsEditing", "handleInputChange", "e", "name", "value", "type", "target", "newValue", "parseFloat", "newData", "handleToggle", "setting", "handlePaymentMethodToggle", "method", "_formData$paymentMeth", "paymentMethods", "handleSubmit", "preventDefault", "handleCancel", "currencies", "code", "symbol", "key", "description", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "currency", "onChange", "disabled", "map", "taxRate", "min", "max", "step", "find", "c", "revenueGoal", "placeholder", "_formData$paymentMeth2", "_formData$paymentMeth3", "_formData$paymentMeth4", "checked", "autoInvoicing", "profitReports", "toLocaleString", "style", "width", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/setting/client/src/components/settings/ProfitSettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Switch } from '@headlessui/react';\nimport { CurrencyDollarIcon, ChartBarIcon } from '@heroicons/react/24/outline';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst ProfitSettings = ({ settings, onUpdate }) => {\n  const [formData, setFormData] = useState(settings || {});\n  const [isEditing, setIsEditing] = useState(false);\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    const newValue = type === 'number' ? parseFloat(value) : value;\n    const newData = { ...formData, [name]: newValue };\n    setFormData(newData);\n  };\n\n  const handleToggle = (setting) => {\n    const newData = { ...formData, [setting]: !formData[setting] };\n    setFormData(newData);\n  };\n\n  const handlePaymentMethodToggle = (method) => {\n    const newData = {\n      ...formData,\n      paymentMethods: {\n        ...formData.paymentMethods,\n        [method]: !formData.paymentMethods?.[method]\n      }\n    };\n    setFormData(newData);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onUpdate(formData);\n    setIsEditing(false);\n  };\n\n  const handleCancel = () => {\n    setFormData(settings);\n    setIsEditing(false);\n  };\n\n  const currencies = [\n    { code: 'USD', name: 'US Dollar', symbol: '$' },\n    { code: 'EUR', name: 'Euro', symbol: '€' },\n    { code: 'GBP', name: 'British Pound', symbol: '£' },\n    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },\n    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },\n    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' }\n  ];\n\n  const paymentMethods = [\n    {\n      key: 'stripe',\n      name: 'Stripe',\n      description: 'Accept credit cards and digital payments',\n      icon: '💳'\n    },\n    {\n      key: 'paypal',\n      name: 'PayPal',\n      description: 'Accept PayPal payments',\n      icon: '🅿️'\n    },\n    {\n      key: 'bankTransfer',\n      name: 'Bank Transfer',\n      description: 'Accept direct bank transfers',\n      icon: '🏦'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Profit & Payment Settings</h3>\n        <p className=\"text-sm text-gray-600\">\n          Configure payment methods, currency, and profit tracking settings.\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit}>\n        {/* Currency & Tax Settings */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <CurrencyDollarIcon className=\"h-5 w-5 mr-2\" />\n            Currency & Tax Settings\n          </h4>\n          \n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label htmlFor=\"currency\" className=\"block text-sm font-medium text-gray-700\">\n                Default Currency\n              </label>\n              <select\n                name=\"currency\"\n                id=\"currency\"\n                value={formData.currency || 'USD'}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              >\n                {currencies.map((currency) => (\n                  <option key={currency.code} value={currency.code}>\n                    {currency.symbol} {currency.name} ({currency.code})\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"taxRate\" className=\"block text-sm font-medium text-gray-700\">\n                Tax Rate (%)\n              </label>\n              <input\n                type=\"number\"\n                name=\"taxRate\"\n                id=\"taxRate\"\n                value={formData.taxRate || 0}\n                onChange={handleInputChange}\n                disabled={!isEditing}\n                min=\"0\"\n                max=\"100\"\n                step=\"0.1\"\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n              />\n            </div>\n\n            <div className=\"sm:col-span-2\">\n              <label htmlFor=\"revenueGoal\" className=\"block text-sm font-medium text-gray-700\">\n                Annual Revenue Goal\n              </label>\n              <div className=\"mt-1 relative rounded-md shadow-sm\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <span className=\"text-gray-500 sm:text-sm\">\n                    {currencies.find(c => c.code === formData.currency)?.symbol || '$'}\n                  </span>\n                </div>\n                <input\n                  type=\"number\"\n                  name=\"revenueGoal\"\n                  id=\"revenueGoal\"\n                  value={formData.revenueGoal || 0}\n                  onChange={handleInputChange}\n                  disabled={!isEditing}\n                  min=\"0\"\n                  className=\"pl-7 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500\"\n                  placeholder=\"50000\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Payment Methods */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4\">Payment Methods</h4>\n          \n          <div className=\"space-y-4\">\n            {paymentMethods.map((method) => (\n              <div key={method.key} className=\"flex items-start justify-between\">\n                <div className=\"flex items-start\">\n                  <span className=\"text-2xl mr-3\">{method.icon}</span>\n                  <div className=\"flex-1\">\n                    <h5 className=\"text-sm font-medium text-gray-900\">{method.name}</h5>\n                    <p className=\"text-sm text-gray-500 mt-1\">{method.description}</p>\n                  </div>\n                </div>\n                <Switch\n                  checked={formData.paymentMethods?.[method.key] || false}\n                  onChange={() => handlePaymentMethodToggle(method.key)}\n                  disabled={!isEditing}\n                  className={classNames(\n                    formData.paymentMethods?.[method.key] ? 'bg-blue-600' : 'bg-gray-200',\n                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                    !isEditing && 'opacity-50 cursor-not-allowed'\n                  )}\n                >\n                  <span className=\"sr-only\">Enable {method.name}</span>\n                  <span\n                    aria-hidden=\"true\"\n                    className={classNames(\n                      formData.paymentMethods?.[method.key] ? 'translate-x-5' : 'translate-x-0',\n                      'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                    )}\n                  />\n                </Switch>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Automation Settings */}\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n          <h4 className=\"text-base font-medium text-gray-900 mb-4 flex items-center\">\n            <ChartBarIcon className=\"h-5 w-5 mr-2\" />\n            Automation & Reporting\n          </h4>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Auto Invoicing</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Automatically generate and send invoices for paid events\n                </p>\n              </div>\n              <Switch\n                checked={formData.autoInvoicing || false}\n                onChange={() => handleToggle('autoInvoicing')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.autoInvoicing ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Auto invoicing</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.autoInvoicing ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <h5 className=\"text-sm font-medium text-gray-900\">Profit Reports</h5>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  Generate monthly profit and revenue reports\n                </p>\n              </div>\n              <Switch\n                checked={formData.profitReports || false}\n                onChange={() => handleToggle('profitReports')}\n                disabled={!isEditing}\n                className={classNames(\n                  formData.profitReports ? 'bg-blue-600' : 'bg-gray-200',\n                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n                  !isEditing && 'opacity-50 cursor-not-allowed'\n                )}\n              >\n                <span className=\"sr-only\">Profit reports</span>\n                <span\n                  aria-hidden=\"true\"\n                  className={classNames(\n                    formData.profitReports ? 'translate-x-5' : 'translate-x-0',\n                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'\n                  )}\n                />\n              </Switch>\n            </div>\n          </div>\n        </div>\n\n        {/* Revenue Summary */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <ChartBarIcon className=\"h-5 w-5 text-blue-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800\">\n                Revenue Goal Progress\n              </h3>\n              <div className=\"mt-2 text-sm text-blue-700\">\n                <p>\n                  Current goal: {currencies.find(c => c.code === formData.currency)?.symbol || '$'}{formData.revenueGoal?.toLocaleString() || '0'}\n                </p>\n                <div className=\"mt-2 bg-blue-200 rounded-full h-2\">\n                  <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '35%' }}></div>\n                </div>\n                <p className=\"mt-1 text-xs\">35% of annual goal achieved</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3\">\n          {isEditing ? (\n            <>\n              <button\n                type=\"button\"\n                onClick={handleCancel}\n                className=\"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Save Changes\n              </button>\n            </>\n          ) : (\n            <button\n              type=\"button\"\n              onClick={() => setIsEditing(true)}\n              className=\"bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Edit Settings\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ProfitSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA;EACjD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAACc,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGH,CAAC,CAACI,MAAM;IACtC,MAAMC,QAAQ,GAAGF,IAAI,KAAK,QAAQ,GAAGG,UAAU,CAACJ,KAAK,CAAC,GAAGA,KAAK;IAC9D,MAAMK,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACM,IAAI,GAAGI;IAAS,CAAC;IACjDT,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,MAAMF,OAAO,GAAG;MAAE,GAAGZ,QAAQ;MAAE,CAACc,OAAO,GAAG,CAACd,QAAQ,CAACc,OAAO;IAAE,CAAC;IAC9Db,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMG,yBAAyB,GAAIC,MAAM,IAAK;IAAA,IAAAC,qBAAA;IAC5C,MAAML,OAAO,GAAG;MACd,GAAGZ,QAAQ;MACXkB,cAAc,EAAE;QACd,GAAGlB,QAAQ,CAACkB,cAAc;QAC1B,CAACF,MAAM,GAAG,GAAAC,qBAAA,GAACjB,QAAQ,CAACkB,cAAc,cAAAD,qBAAA,eAAvBA,qBAAA,CAA0BD,MAAM,CAAC;MAC9C;IACF,CAAC;IACDf,WAAW,CAACW,OAAO,CAAC;EACtB,CAAC;EAED,MAAMO,YAAY,GAAId,CAAC,IAAK;IAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBzB,QAAQ,CAACK,QAAQ,CAAC;IAClBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBpB,WAAW,CAACP,QAAQ,CAAC;IACrBS,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMmB,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,WAAW;IAAEkB,MAAM,EAAE;EAAI,CAAC,EAC/C;IAAED,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,MAAM;IAAEkB,MAAM,EAAE;EAAI,CAAC,EAC1C;IAAED,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,eAAe;IAAEkB,MAAM,EAAE;EAAI,CAAC,EACnD;IAAED,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,iBAAiB;IAAEkB,MAAM,EAAE;EAAK,CAAC,EACtD;IAAED,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,mBAAmB;IAAEkB,MAAM,EAAE;EAAK,CAAC,EACxD;IAAED,IAAI,EAAE,KAAK;IAAEjB,IAAI,EAAE,cAAc;IAAEkB,MAAM,EAAE;EAAI,CAAC,CACnD;EAED,MAAMN,cAAc,GAAG,CACrB;IACEO,GAAG,EAAE,QAAQ;IACbnB,IAAI,EAAE,QAAQ;IACdoB,WAAW,EAAE,0CAA0C;IACvDC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,GAAG,EAAE,QAAQ;IACbnB,IAAI,EAAE,QAAQ;IACdoB,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,GAAG,EAAE,cAAc;IACnBnB,IAAI,EAAE,eAAe;IACrBoB,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA;QAAI2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrFhD,OAAA;QAAG2C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhD,OAAA;MAAMiD,QAAQ,EAAEf,YAAa;MAAAU,QAAA,gBAE3B5C,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACH,kBAAkB;YAAC8C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOkD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEqB,IAAI,EAAC,UAAU;cACf8B,EAAE,EAAC,UAAU;cACb7B,KAAK,EAAEP,QAAQ,CAACqC,QAAQ,IAAI,KAAM;cAClCC,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrB0B,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,EAEjKP,UAAU,CAACkB,GAAG,CAAEH,QAAQ,iBACvBpD,OAAA;gBAA4BsB,KAAK,EAAE8B,QAAQ,CAACd,IAAK;gBAAAM,QAAA,GAC9CQ,QAAQ,CAACb,MAAM,EAAC,GAAC,EAACa,QAAQ,CAAC/B,IAAI,EAAC,IAAE,EAAC+B,QAAQ,CAACd,IAAI,EAAC,GACpD;cAAA,GAFac,QAAQ,CAACd,IAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAOkD,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEuB,IAAI,EAAC,QAAQ;cACbF,IAAI,EAAC,SAAS;cACd8B,EAAE,EAAC,SAAS;cACZ7B,KAAK,EAAEP,QAAQ,CAACyC,OAAO,IAAI,CAAE;cAC7BH,QAAQ,EAAElC,iBAAkB;cAC5BmC,QAAQ,EAAE,CAACrC,SAAU;cACrBwC,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTC,IAAI,EAAC,KAAK;cACVhB,SAAS,EAAC;YAAwJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhD,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5C,OAAA;cAAOkD,OAAO,EAAC,aAAa;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEjF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK2C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjD5C,OAAA;gBAAK2C,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF5C,OAAA;kBAAM2C,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACvC,EAAAhC,gBAAA,GAAAyB,UAAU,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKvB,QAAQ,CAACqC,QAAQ,CAAC,cAAAxC,gBAAA,uBAAlDA,gBAAA,CAAoD2B,MAAM,KAAI;gBAAG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhD,OAAA;gBACEuB,IAAI,EAAC,QAAQ;gBACbF,IAAI,EAAC,aAAa;gBAClB8B,EAAE,EAAC,aAAa;gBAChB7B,KAAK,EAAEP,QAAQ,CAAC+C,WAAW,IAAI,CAAE;gBACjCT,QAAQ,EAAElC,iBAAkB;gBAC5BmC,QAAQ,EAAE,CAACrC,SAAU;gBACrBwC,GAAG,EAAC,GAAG;gBACPd,SAAS,EAAC,wJAAwJ;gBAClKoB,WAAW,EAAC;cAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7EhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBX,cAAc,CAACsB,GAAG,CAAExB,MAAM;YAAA,IAAAiC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAAA,oBACzBlE,OAAA;cAAsB2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAChE5C,OAAA;gBAAK2C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B5C,OAAA;kBAAM2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEb,MAAM,CAACW;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDhD,OAAA;kBAAK2C,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrB5C,OAAA;oBAAI2C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEb,MAAM,CAACV;kBAAI;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpEhD,OAAA;oBAAG2C,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEb,MAAM,CAACU;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhD,OAAA,CAACJ,MAAM;gBACLuE,OAAO,EAAE,EAAAH,sBAAA,GAAAjD,QAAQ,CAACkB,cAAc,cAAA+B,sBAAA,uBAAvBA,sBAAA,CAA0BjC,MAAM,CAACS,GAAG,CAAC,KAAI,KAAM;gBACxDa,QAAQ,EAAEA,CAAA,KAAMvB,yBAAyB,CAACC,MAAM,CAACS,GAAG,CAAE;gBACtDc,QAAQ,EAAE,CAACrC,SAAU;gBACrB0B,SAAS,EAAExC,UAAU,CACnB,CAAA8D,sBAAA,GAAAlD,QAAQ,CAACkB,cAAc,cAAAgC,sBAAA,eAAvBA,sBAAA,CAA0BlC,MAAM,CAACS,GAAG,CAAC,GAAG,aAAa,GAAG,aAAa,EACrE,wNAAwN,EACxN,CAACvB,SAAS,IAAI,+BAChB,CAAE;gBAAA2B,QAAA,gBAEF5C,OAAA;kBAAM2C,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,SAAO,EAACb,MAAM,CAACV,IAAI;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDhD,OAAA;kBACE,eAAY,MAAM;kBAClB2C,SAAS,EAAExC,UAAU,CACnB,CAAA+D,sBAAA,GAAAnD,QAAQ,CAACkB,cAAc,cAAAiC,sBAAA,eAAvBA,sBAAA,CAA0BnC,MAAM,CAACS,GAAG,CAAC,GAAG,eAAe,GAAG,eAAe,EACzE,4HACF;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA,GA1BDjB,MAAM,CAACS,GAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bf,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5C,OAAA;UAAI2C,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxE5C,OAAA,CAACF,YAAY;YAAC6C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5C,OAAA;YAAK2C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C5C,OAAA;cAAK2C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5C,OAAA;gBAAI2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEhD,OAAA;gBAAG2C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhD,OAAA,CAACJ,MAAM;cACLuE,OAAO,EAAEpD,QAAQ,CAACqD,aAAa,IAAI,KAAM;cACzCf,QAAQ,EAAEA,CAAA,KAAMzB,YAAY,CAAC,eAAe,CAAE;cAC9C0B,QAAQ,EAAE,CAACrC,SAAU;cACrB0B,SAAS,EAAExC,UAAU,CACnBY,QAAQ,CAACqD,aAAa,GAAG,aAAa,GAAG,aAAa,EACtD,wNAAwN,EACxN,CAACnD,SAAS,IAAI,+BAChB,CAAE;cAAA2B,QAAA,gBAEF5C,OAAA;gBAAM2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ChD,OAAA;gBACE,eAAY,MAAM;gBAClB2C,SAAS,EAAExC,UAAU,CACnBY,QAAQ,CAACqD,aAAa,GAAG,eAAe,GAAG,eAAe,EAC1D,4HACF;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhD,OAAA;YAAK2C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C5C,OAAA;cAAK2C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5C,OAAA;gBAAI2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEhD,OAAA;gBAAG2C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhD,OAAA,CAACJ,MAAM;cACLuE,OAAO,EAAEpD,QAAQ,CAACsD,aAAa,IAAI,KAAM;cACzChB,QAAQ,EAAEA,CAAA,KAAMzB,YAAY,CAAC,eAAe,CAAE;cAC9C0B,QAAQ,EAAE,CAACrC,SAAU;cACrB0B,SAAS,EAAExC,UAAU,CACnBY,QAAQ,CAACsD,aAAa,GAAG,aAAa,GAAG,aAAa,EACtD,wNAAwN,EACxN,CAACpD,SAAS,IAAI,+BAChB,CAAE;cAAA2B,QAAA,gBAEF5C,OAAA;gBAAM2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ChD,OAAA;gBACE,eAAY,MAAM;gBAClB2C,SAAS,EAAExC,UAAU,CACnBY,QAAQ,CAACsD,aAAa,GAAG,eAAe,GAAG,eAAe,EAC1D,4HACF;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE5C,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5C,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5C,OAAA,CAACF,YAAY;cAAC6C,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNhD,OAAA;YAAK2C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5C,OAAA;cAAI2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhD,OAAA;cAAK2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC5C,OAAA;gBAAA4C,QAAA,GAAG,gBACa,EAAC,EAAA/B,iBAAA,GAAAwB,UAAU,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKvB,QAAQ,CAACqC,QAAQ,CAAC,cAAAvC,iBAAA,uBAAlDA,iBAAA,CAAoD0B,MAAM,KAAI,GAAG,EAAE,EAAAzB,qBAAA,GAAAC,QAAQ,CAAC+C,WAAW,cAAAhD,qBAAA,uBAApBA,qBAAA,CAAsBwD,cAAc,CAAC,CAAC,KAAI,GAAG;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9H,CAAC,eACJhD,OAAA;gBAAK2C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChD5C,OAAA;kBAAK2C,SAAS,EAAC,8BAA8B;kBAAC4B,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAM;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACNhD,OAAA;gBAAG2C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxC3B,SAAS,gBACRjB,OAAA,CAAAE,SAAA;UAAA0C,QAAA,gBACE5C,OAAA;YACEuB,IAAI,EAAC,QAAQ;YACbkD,OAAO,EAAErC,YAAa;YACtBO,SAAS,EAAC,2LAA2L;YAAAC,QAAA,EACtM;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACEuB,IAAI,EAAC,QAAQ;YACboB,SAAS,EAAC,+LAA+L;YAAAC,QAAA,EAC1M;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHhD,OAAA;UACEuB,IAAI,EAAC,QAAQ;UACbkD,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,IAAI,CAAE;UAClCyB,SAAS,EAAC,+LAA+L;UAAAC,QAAA,EAC1M;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CApTIH,cAAc;AAAAkE,EAAA,GAAdlE,cAAc;AAsTpB,eAAeA,cAAc;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}